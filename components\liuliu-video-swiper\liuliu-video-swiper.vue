<template>
	<view class="swiper_container" :style="{height:bannerHeight,borderRadius:bannerRadius}">
		<swiper v-if="!isCurrentVideo" :current="swiperPosition" @change="switchItem" circular
			style="width: 100%;height: 100%;" :autoplay="isAutoplay" :indicator-dots="isShowSwiperIndicatorDots">
			<swiper-item v-for="item in adList" style="width: 100%;height: 100%;">
				<image v-if="!item.isVideo" :src="item.url" style="width: 100%;height: 100%;overflow: hidden;"
					:style="{borderRadius:bannerRadius}">
				</image>
			</swiper-item>
		</swiper>

		<view v-else class="video_container"
			:style="{paddingBottom:bannerRadius,paddingTop:bannerRadius,borderRadius:bannerRadius}">
			<video id="myVideo" :src="videoUrl" autoplay="true" style="width: 100%;height: 100%;" :controls="false"
				:enable-progress-gesture="false" @ended="videoEnd">
			</video>
		</view>
		<!-- #ifdef H5 || MP-WEIXIN -->
		<view class="video_mask_container">
			<video-mask></video-mask>
		</view>
		<!-- #endif -->
	</view>
</template>
<script>
	import videoMask from './liuliu-video-mask.vue'
	// #ifdef APP-PLUS
	const subNvue = uni.getSubNVueById('videoMask'); //获取
	// #endif
	export default {
		components: {
			videoMask,
		},
		computed: {
			bannerHeight() {
				return uni.upx2px(this.adHeight) + 'px'
			},
			bannerRadius() {
				return uni.upx2px(this.adRadius) + 'px'
			},
		},
		props: {
			adHeight: { //轮播图高度
				type: Number,
				default: 440
			},
			adRadius: { //轮播图圆角
				type: Number,
				default: 12
			},
			adList: { //广告数据
				type: Array,
				default () {
					return []
				}
			},
		},
		data() {
			return {
				swiperPosition: 0,
				isCurrentVideo: false,
				videoUrl: "",
				videoContext: null,
				isAutoplay: true, //是否自动轮播
				isShowSwiperIndicatorDots: false,
				showVideoTimer: null //显示video遮罩的timer
			}
		},
		mounted() {
			let that = this
			that.loadSubNVue()
			that.initVideo()
			that.setVideoInfo(0)
			//监听广告点击事件
			uni.$on('adClick', function(position) {
				that.handleAdClick(position)
			})
			//监听页面滚动
			uni.$on('onPageScroll', function() {
				// #ifdef APP-PLUS
				//console.log('开始滚动')
				if (that.showVideoTimer != null) {
					clearTimeout(that.showVideoTimer)
				}
				const subNvue = uni.getSubNVueById('videoMask'); //获取
				subNvue.hide()
				that.showVideoTimer = setTimeout(function() {
					that.setVideoMaskLocation()
				}, 500)
				// #endif
			})
		},
		destroyed() {
			this.unloadSubNVue()
			//取消监听广告点击事件
			uni.$off('adClick')
			//取消监听页面滚动
			uni.$off('onPageScroll')
			console.log('video-swiper destroyed')
		},
		methods: {
			loadSubNVue() {
				let that = this
				// #ifdef APP-PLUS
				that.setVideoMaskLocation()
				// #endif
				// 监听事件，监听video-mask页面传过来的
				uni.$on('swiperChangeDirection', (direction) => {
					//根据是左滑，还是右滑，切换轮播图
					if (direction.elePosition == "left") {
						if (that.swiperPosition >= that.adList.length - 1) {
							that.swiperPosition = 0
							console.log(that.swiperPosition)
						} else {
							that.swiperPosition += 1
							console.log(that.swiperPosition)
						}

					} else if (direction.elePosition == "right") {
						if (that.swiperPosition <= 0) {
							that.swiperPosition = that.adList.length - 1
						} else {
							that.swiperPosition -= 1
						}
					}
					that.setVideoInfo(that.swiperPosition)
				})
				//初始化nvue数据
				setTimeout(function() {
					uni.$emit('init_param', that.adList)
					uni.$emit('init_current', that.swiperPosition)
				}, 200)
			},
			unloadSubNVue() {
				// #ifdef APP-PLUS
				//要在页面关闭的时候， 移除监听事件 ，隐藏遮罩层
				uni.$off('swiperChangeDirection');
				subNvue.hide()
				// #endif
			},
			initVideo() {
				let that = this
				that.videoContext = uni.createVideoContext('myVideo')
			},
			//轮播图切换
			switchItem(obj) {
				console.log(obj)
				if (obj != undefined) {
					let position = obj.target.current
					this.setVideoInfo(position)
				}
			},

			setVideoInfo(position) {
				let item = this.adList[position]
				this.isCurrentVideo = item.isVideo
				if (this.isCurrentVideo) {
					this.videoUrl = item.url
					this.videoContext.stop()
					this.videoContext.play()
					this.isAutoplay = false
				} else {
					this.videoContext.stop()
					this.isAutoplay = true
				}
				console.log('setVideoInfo' + position)
				this.swiperPosition = position
				uni.$emit('init_current', position)
			},
			videoEnd() {
				console.log("vido ended")
				console.log("swiperPosition = " + this.swiperPosition)
				this.setVideoInfo(this.swiperPosition + 1)
			},
			handleAdClick(position) {
				this.$emit('handleAdClick', position)
			},
			//设置视频遮罩的位置
			setVideoMaskLocation() {
				let that = this
				let query = uni.createSelectorQuery().in(that);
				query.select('.swiper_container').boundingClientRect(data => {
					uni.getSystemInfo({
						success: (res) => {
							let top = data.top + res.statusBarHeight + 44 //44是原生的标题高度,兼容性有待进一步测试
							const subNvue = uni.getSubNVueById('videoMask'); //获取
							subNvue.setStyle({
								'left': data.left + 'px',
								'top': top + 'px',
								'width': data.width + 'px',
								'height': uni.upx2px(that.adHeight) + 'px'
							})
							subNvue.show()
							console.log(data.left + "," + top + "," + data.width + "," + uni.upx2px(that.adHeight))
						}
					})
				}).exec();
			}
		}
	}
</script>

<style scoped lang="scss">
	@import "./liuliu-video-swiper.scss";
</style>