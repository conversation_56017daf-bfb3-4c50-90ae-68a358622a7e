<template>
	<view class="pagination">
	  <view class="total">总计<text class="amount">{{ props.total }}</text>款<text v-if="roleId === 1"><text class="amount">{{ props.totalAmount }}</text>件，总价<text class="amount">{{ props.totalPrice }}</text></text></view>
	</view>
	<view class="mask"></view>
</template>

<script setup>
import { ref, watch } from 'vue'
	
const emits = defineEmits(['change'])
const props = defineProps({
	pageSize: {
		type: Number,
		default: 20
	},
	total: {
		type: Number,
		default: 0
	},
	totalAmount: {
		type: Number,
		default: 0
	},
	totalPrice: {
		type: Number,
		default: 0
	},
	page: {
		type: Number,
		default: 1
	},
	roleId:{
		type: Number,
		default: 0
	}
})
watch(
	() => props.total,
	() => {
		init()
	}
)

const current = ref(1)
const max = ref(1)

const handlePrev = () => {
	if (current.value === 1) return
	current.value -= 1
	emits('change', {
		type: 'prev',
		value: current.value
	})
}
const handleNext = () => {
	if (current.value === max) return
	current.value += 1
	emits('change', {
		type: 'next',
		value: current.value
	})
}

const init = () => {
	max.value = Math.ceil(props.total / props.pageSize) || 1
}
</script>

<style lang="scss" scoped>
.pagination {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  background-color: $uni-bg-color;
  box-shadow: 0 6rpx 12rpx 4rpx rgba(0, 0, 0, 0.05),
							0 16rpx 10rpx 2rpx rgba(0, 0, 0, 0.06),
							0 10rpx 10rpx -6rpx rgba(0, 0, 0, 0.05);
  .total {
    display: flex;
    align-items: center;
    justify-content: center;
    color: $uni-text-color-grey;
    height: 120rpx;
  }
  .pagin {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20rpx;
    height: 80rpx;
    .text {
      font-size: 32rpx;
      .current {
				color: $uni-color-primary;
      }
    }
  }
}
.mask {
  height: 120rpx;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
.amount{
	color: red;
	margin:10rpx;
}
</style>