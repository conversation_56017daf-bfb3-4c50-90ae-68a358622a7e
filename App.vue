<script>
	import {
		version,
		name
	} from './package.json'
	import {
		versionName
	} from '@/manifest.json'
	import consoleImgs from '@/common/consoleImgs.js'
	// #ifdef APP-PLUS
	import appUpgrade from '@/common/appUpgrade.js';
	const TUICalling = uni.requireNativePlugin("TUICallingUniPlugin-TUICallingModule");
	// #endif
	 
	export default {
		onLaunch: function() {
			// #ifdef H5
			// console.log(
			// 	`%c 考拉Team ${name} %c v${version} `,
			// 	'background:#35495e ; padding: 1px; border-radius: 3px 0 0 3px;  color: #fff',
			// 	'background:#007aff ;padding: 1px; border-radius: 0 3px 3px 0;  color: #fff; font-weight: bold;'
			// )
			// console.log(consoleImgs.fz)
			// todo 下列两行
			uni.setStorageSync('device', 'H5');
			uni.setStorageSync('version', versionName);
			// this.$http.request({
			// 	url: '/common/getVersion',
			// 	success: (res) => {
			// 		if(res.data.data.upgrade=='Y'){
			// 			console.log(
			// 				`%c 有新版本 `+res.data.data.version,
			// 				'background:#007aff ;padding: 1px; border-radius: 0 3px 3px 0;  color: #fff; font-weight: bold;'
			// 			)
			// 		}
			// 	}
			// });
			// #endif
			console.log('App Launch')
			let token= uni.getStorageSync('Authorization');
			console.log(token)
			if (!token) {
				//不存在则跳转至登录页
				// #ifdef APP-PLUS
				plus.navigator.closeSplashscreen();
				// #endif
			} else {
				this.$store_im.dispatch('get_UserInfo').then(res=>{
					console.log(res)
					// #ifdef APP-PLUS
					var nickName=res.nickName
					var portrait=res.portrait
					this.$http.request({
						url: '/trtc/getSign',
						success: (res) => {
							var sdkAppID=res.data.data.appId
							var userID=res.data.data.userId
							var userSig=res.data.data.sign
							TUICalling.login({//登录音视频
							    sdkAppID: sdkAppID, 
							    userID: userID,
							    userSig: userSig
							},(res) => {
							    console.log('音视频登录成功')
								TUICalling.setUserNickname({
								    nickName: nickName
								})
								TUICalling.setUserAvatar({
								    avatar: portrait
								})
								plus.io.requestFileSystem(plus.io.PRIVATE_WWW, function(fs) {
								    fs.root.getFile('/static/im/longcall.mp3', {
								        create: false
								    }, function(fileEntry) {
								        fileEntry.file(function(file) {
											TUICalling.setCallingBell({
											    ringtone: file.fullPath
											},(res) => {
												console.log(JSON.stringify(res))
											})
										});
								    });
								});
								
							})
						}
					});
					this.$http.request({
						url: '/my/refresh',
						success: (res) => {}
					});
					// #endif
					this.$socketTask.connectSocket()
				})
				uni.reLaunch({
					//url: "wx/tabbar1/index",
				}).then(res=>{
					// #ifdef APP-PLUS
					plus.navigator.closeSplashscreen();
					// #endif
				})
			}
			// #ifdef APP-PLUS
			//升级检测
			uni.getSystemInfo({
				success: (res)=> {
					uni.setStorageSync('device', res.platform);
					plus.runtime.getProperty(plus.runtime.appid, (widgetInfo)=> {
						uni.setStorageSync('version', widgetInfo.version);
						// this.$http.request({
						// 	url: '/common/getVersion',
						// 	success: (res) => {
						// 		if(res.data.data.upgrade=='Y'){
						// 			appUpgrade.init({
						// 				titleText: '版本更新'+res.data.data.version,
						// 				packageUrl:res.data.data.url,
						// 				content: res.data.data.content,
						// 				forceUpgrade:res.data.data.forceUpgrade=='Y' ? true : false
						// 			});
						// 			appUpgrade.show();
						// 		}
						// 	}
						// });
					});
				}
			});
			uni.onNetworkStatusChange( (res)=> {
				if(res.isConnected){
					this.$store_im.dispatch('get_UserInfo')
				}
			});
			// #endif
		},
		onShow: function() {
			console.log('App Show')
			uni.getStorage({
				key: 'call',
				success: (res) => {
					var callx=res.data
					if(callx){
						var call=JSON.parse(callx)
						function getInervalHour(startDate) {//获取两个时间之间的小时
							if (!startDate) {
								return '0秒'
							}
							var ms = new Date().getTime() - startDate;
							if (ms < 0) return '0秒';
							if((ms/1000)<60){
								return Math.floor(ms / 1000)+'秒';
							}else{
								return Math.floor(ms / 1000 /60)+'分';
							}
						}
						var msgType=''
						if(call.type=='audio'){
							msgType='TRTC_VOICE_END'
						}
						if(call.type=='video'){
							msgType='TRTC_VIDEO_END'
						}
						this.$fc.pushOutMsg({
							msgContent:getInervalHour(call.startTime),
							msgType:msgType,
							windowType:'SINGLE',
							userId:call.userId,
						})
						uni.removeStorageSync('call')
					}
				}
			});
		},
		onHide: function() {
			console.log('App Hide')
		}
	}
</script>

<style lang="scss">
	
@import url("static/icon.css");
@import url("static/iconExt.css");

/*每个页面公共css */
// @import '@/uni_modules/uni-scss/index.scss';
@import "@/pagesSub/static/im/styles/animation.css";

@import '@/pagesSub/static/im/customicons.css';

// 设置整个项目的背景色
page {
	box-sizing: border-box;
}

page {
	background-color: $uni-bg-color-grey;
	color: $uni-text-color;
	font-size: 32rpx;
}

.btn {
	position: relative;
	border: 0;
	display: inline-flex;
	align-items: center;
	justify-content: center;
	box-sizing: border-box;
	padding: 0 30rpx;
	font-size: 28rpx;
	height: 64rpx;
	line-height: 1;
	text-align: center;
	text-decoration: none;
	overflow: visible;
	margin-left: initial;
	transform: translate(0, 0);
	margin-right: initial;
	&::after {
		display: none;
	}
	&.button-hover {
		transform: translate(1rpx, 1rpx);
	}
}

.search {
	display: grid;
	grid-template-columns: repeat(2, minmax(0, 1fr));
	padding: 20rpx;
	gap: 20rpx;
	background-color: $uni-bg-color;
	.item {
		display: flex;
		align-items: center;
		gap: 20rpx;
		font-size: 28rpx;
		.label {
			flex-shrink: 0;
		}
		.input {
			flex: 1;
			background-color: $uni-bg-color-grey;
			border-radius: 10rpx;
			padding: 0 20rpx;
			min-height: 60rpx;
			.value {
				display: flex;
				align-items: center;
				justify-content: space-between;
				min-height: 60rpx;
				&::after {
					font-family: 'iconfont';
					content: '\e840';
					color: $uni-text-color-grey;
				}
				&:empty::before {
					content: '请选择';
					color: gray;
				}
			}
		}
	}
	.submit {
		background-color: $uni-color-primary;
		color: $uni-text-color-inverse;
	}
}

.form {
	--label: 160rpx;
	--bg-color: #fff;
	display: block;
	background-color: var(--bg-color, #fff);
	font-size: 28rpx;
	.item {
		display: flex;
		align-items: center;
		justify-content: space-between;
		gap: 20rpx;
		min-height: 100rpx;
		padding: 0 20rpx;
		&.column {
      align-items: flex-start;
      flex-direction: column;
			.label,
			.input {
				width: 100%;
			}
		}
		.label {
			display: block;
			width: var(--label);
		}
		.input {
			flex: 1;
			min-height: 60rpx;
			padding: 0 20rpx;
			background-color: $uni-bg-color-grey;
			border-radius: $uni-border-radius-base;
			font-size: 28rpx;
			.value {
				display: flex;
				align-items: center;
				justify-content: space-between;
				min-height: 60rpx;
				&::after {
					font-family: 'iconfont';
					content: '\e840';
					color: $uni-text-color-grey;
				}
				&:empty::before {
					content: attr(data-tips);
					color: gray;
				}
			}
		}
	}
	.submit {
		display: flex;
		align-items: center;
		justify-content: center;
		height: 80rpx;  
		background-color: $uni-color-primary;
		color: #fff;
		margin: 50rpx 20rpx;
	}
}

.price {
  color: $uni-color-error;
  font-weight: bold;
  &::before {
    content: '￥';
    font-size: 80%;
  }
}

/* #ifndef APP-NVUE */
uni-video {
  width: 100%;
  height: 100%;
}
/* #endif */
</style>
