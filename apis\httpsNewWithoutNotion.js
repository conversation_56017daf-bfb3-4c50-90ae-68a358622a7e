const httpsNewWithoutNotion = (_url, data, args) => {
	return new Promise((resolve, reject) => {
		const {
			method = 'POST',
			header,
			isLoad = true
		} = args
		let isShowLoading = false;
		if(isLoad){
				uni.showLoading({
				title: '加载中...',
				mask: true
			})
			isShowLoading = true;
		}
		// const url = baseUrl + '/api/' + _url
		const url = _url
		uni.request({
			url,
			method,
			data,
			header,
			success: (res) => {
				const { data } = res
				// console.log(res)
				isShowLoading&&uni.hideLoading()
				switch(data.ErrCode) {
					// console.log(data.ErrCode)
					case '0':
						resolve(data)
						break
					default:
						// uni.showToast({
						// 	title: data.ErrMsg || '网络出错',
						// 	icon: 'none'
						// })
						// if (data.data.reload) {
						// 	uni.clearStorageSync()
						// 	uni.reLaunch({
						// 		url: '/pagesSub/pages/login/login'
						// 	})
						// }
						resolve(data)
						// reject(data.msg || '网络出错')
						break
				}
			},
			complete: err => {
				isShowLoading&&uni.hideLoading()
				console.log(url, data, err)
			}
		})
	})
}

export default httpsNewWithoutNotion