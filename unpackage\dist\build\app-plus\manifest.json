{"@platforms": ["android", "iPhone", "iPad"], "id": "__UNI__2B24B5E", "name": "睿睿", "version": {"name": "1.3.7", "code": 114}, "description": "", "developer": {"name": "", "email": "", "url": ""}, "permissions": {"Barcode": {}, "Camera": {}, "Payment": {}, "Geolocation": {}, "iBeacon": {}, "Maps": {"coordType": "gcj02"}, "Push": {}, "Speech": {}, "VideoPlayer": {}, "Bluetooth": {}, "UniNView": {"description": "UniNView原生渲染"}}, "plus": {"useragent": {"value": "uni-app", "concatenate": true}, "splashscreen": {"target": "id:1", "autoclose": true, "waiting": false, "delay": 0}, "popGesture": "close", "launchwebview": {"id": "1", "kernel": "WKWebview"}, "nvueCompiler": "uni-app", "usingComponents": true, "nvueStyleCompiler": "uni-app", "compilerVersion": 3, "compatible": {"ignoreVersion": true}, "distribute": {"icons": {"android": {"hdpi": "static/logo.png", "xhdpi": "static/logo.png", "xxhdpi": "static/logo.png", "xxxhdpi": "static/logo.png"}, "ios": {"appstore": "", "ipad": {"app": "", "app@2x": "", "notification": "", "notification@2x": "", "proapp@2x": "", "settings": "", "settings@2x": "", "spotlight": "", "spotlight@2x": ""}, "iphone": {"app@2x": "", "app@3x": "", "notification@2x": "", "notification@3x": "", "settings@2x": "", "settings@3x": "", "spotlight@2x": "", "spotlight@3x": ""}}}, "splashscreen": {"androidStyle": "default", "iosStyle": "common", "android": {"hdpi": "static/first.png", "xhdpi": "static/first.png", "xxhdpi": "static/first.png"}, "useOriginalMsgbox": false}, "google": {"permissions": ["<uses-feature android:name=\"android.hardware.camera\"/>", "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>", "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>", "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>", "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.BLUETOOTH\"/>", "<uses-permission android:name=\"android.permission.BLUETOOTH_ADMIN\"/>", "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>", "<uses-permission android:name=\"android.permission.CAMERA\"/>", "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>", "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>", "<uses-permission android:name=\"android.permission.INTERNET\"/>", "<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\"/>", "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>", "<uses-permission android:name=\"android.permission.READ_LOGS\"/>", "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>", "<uses-permission android:name=\"android.permission.REQUEST_INSTALL_PACKAGES\"/>", "<uses-permission android:name=\"android.permission.VIBRATE\"/>", "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>", "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\"/>", "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"], "minSdkVersion": 21}, "apple": {"dSYMs": false}, "plugins": {"ad": {}, "payment": {"alipay": {"__platform__": ["android"]}, "weixin": {"__platform__": ["android"], "appid": "wx3ae5179266ede20f", "UniversalLinks": ""}}, "geolocation": {"system": {"__platform__": ["ios", "android"]}, "amap": {"__platform__": ["ios", "android"], "appkey_ios": "81cc6c72aeb6a1946510cc1e9f87ee80", "appkey_android": "81cc6c72aeb6a1946510cc1e9f87ee80"}}, "maps": {"amap": {"appkey_ios": "81cc6c72aeb6a1946510cc1e9f87ee80", "appkey_android": "81cc6c72aeb6a1946510cc1e9f87ee80"}}, "push": {"unipush": {"icons": {"small": {"ldpi": "static/im/push/18_1.png", "mdpi": "static/im/push/24_1.png", "hdpi": "static/im/push/36_1.png", "xhdpi": "static/im/push/48_1.png", "xxhdpi": "static/im/push/72_1.png"}}}}, "speech": {}, "audio": {"mp3": {"description": "Android平台录音支持MP3格式文件"}}}}, "nativePlugins": {"TUICallingUniPlugin-TUICallingModule": {"__plugin_info__": {"name": "TUICallingUniPlugin-TUICallingModule", "description": "腾讯云音视频插件", "platforms": "Android,iOS", "url": "", "android_package_name": "", "ios_bundle_id": "", "isCloud": false, "bought": -1, "pid": "", "parameters": {}}}}, "statusbar": {"immersed": "supportedDevice", "style": "dark", "background": "#F8F8F8"}, "uniStatistics": {"enable": false}, "allowsInlineMediaPlayback": true, "safearea": {"background": "#FFFFFF", "bottom": {"offset": "auto"}}, "uni-app": {"control": "uni-v3", "vueVersion": "3", "compilerVersion": "4.66", "nvueCompiler": "uni-app", "renderer": "auto", "nvue": {"flex-direction": "column"}, "nvueLaunchMode": "normal", "webView": {"minUserAgentVersion": "49.0"}}, "tabBar": {"position": "bottom", "color": "#999", "selectedColor": "#007aff", "borderStyle": "rgba(0,0,0,0.4)", "blurEffect": "none", "fontSize": "10px", "iconWidth": "24px", "spacing": "3px", "height": "50px", "list": [{"iconPath": "/static/tabbar/home.png", "selectedIconPath": "/static/tabbar/home_on.png", "pagePath": "pages/index/index", "text": "首页"}, {"iconPath": "/static/tabbar/my.png", "selectedIconPath": "/static/tabbar/my_on.png", "pagePath": "pages/my/my", "text": "我的"}], "selectedIndex": 0, "shown": true, "child": ["lauchwebview"], "selected": 0}}, "app-harmony": {"useragent": {"value": "uni-app", "concatenate": true}, "uniStatistics": {"enable": false}, "safearea": {"background": "#FFFFFF", "bottom": {"offset": "auto"}}}, "launch_path": "__uniappview.html"}