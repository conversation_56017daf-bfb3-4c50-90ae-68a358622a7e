{
    "name" : "睿睿",
    "appid" : "__UNI__2B24B5E",
    "description" : "睿睿财会 - 专业的服装加工管理系统",
    "versionName" : "1.3.7",
    "versionCode" : 114,
    "developer" : {
        "name" : "睿睿科技",
        "email" : "<EMAIL>",
        "url" : "https://www.ruiruicaikuai.com"
    },
    "transformPx" : false,
    /* 5+App特有相关 */
    "app-plus" : {
        //"renderer": "native", //App端纯原生渲染模式
        "nvueCompiler" : "uni-app", //是否启用 uni-app 模式
        "usingComponents" : true,
        "nvueStyleCompiler" : "uni-app",
        "compilerVersion" : 3,
        "compatible" : {
            "ignoreVersion" : true
        },
        "splashscreen" : {
            "alwaysShowBeforeRender" : true,
            "waiting" : false,
            "autoclose" : true,
            "delay" : 0
        },
        /* 模块配置 */
        "modules" : {
            "Barcode" : {},
            "Camera" : {},
            "Payment" : {},
            "Geolocation" : {},
            "iBeacon" : {},
            "Maps" : {},
            "Push" : {},
            "Speech" : {},
            "VideoPlayer" : {},
            "Bluetooth" : {}
        },
        /* 应用发布信息 */
        "distribute" : {
            /* android打包配置 */
            "android" : {
                "permissions" : [
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.BLUETOOTH\"/>",
                    "<uses-permission android:name=\"android.permission.BLUETOOTH_ADMIN\"/>",
                    "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                    "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
                    "<uses-permission android:name=\"android.permission.INTERNET\"/>",
                    "<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.REQUEST_INSTALL_PACKAGES\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"
                ],
                "minSdkVersion" : 21,
                "targetSdkVersion" : 33,
                "abiFilters" : [ "armeabi-v7a", "arm64-v8a", "x86" ]
            },
            /* ios打包配置 */
            "ios" : {
                "dSYMs" : false
            },
            /* SDK配置 */
            "sdkConfigs" : {
                "ad" : {},
                "payment" : {
                    "alipay" : {
                        "__platform__" : [ "android" ]
                    },
                    "weixin" : {
                        "__platform__" : [ "android" ],
                        "appid" : "wx3ae5179266ede20f",
                        "UniversalLinks" : ""
                    }
                },
                "geolocation" : {
                    "system" : {
                        "__platform__" : [ "ios", "android" ]
                    },
                    "amap" : {
                        "__platform__" : [ "ios", "android" ],
                        "appkey_ios" : "81cc6c72aeb6a1946510cc1e9f87ee80",
                        "appkey_android" : "81cc6c72aeb6a1946510cc1e9f87ee80"
                    }
                },
                "maps" : {
                    "amap" : {
                        "appkey_ios" : "81cc6c72aeb6a1946510cc1e9f87ee80",
                        "appkey_android" : "81cc6c72aeb6a1946510cc1e9f87ee80"
                    }
                },
                "push" : {
                    "unipush" : {
                        "icons" : {
                            "small" : {
                                "ldpi" : "static/im/push/18_1.png",
                                "mdpi" : "static/im/push/24_1.png",
                                "hdpi" : "static/im/push/36_1.png",
                                "xhdpi" : "static/im/push/48_1.png",
                                "xxhdpi" : "static/im/push/72_1.png"
                            }
                        }
                    }
                },
                "speech" : {}
            },
            "icons" : {
                "android" : {
                    "hdpi" : "static/logo.png",
                    "xhdpi" : "static/logo.png",
                    "xxhdpi" : "static/logo.png",
                    "xxxhdpi" : "static/logo.png"
                },
                "ios" : {
                    "appstore" : "",
                    "ipad" : {
                        "app" : "",
                        "app@2x" : "",
                        "notification" : "",
                        "notification@2x" : "",
                        "proapp@2x" : "",
                        "settings" : "",
                        "settings@2x" : "",
                        "spotlight" : "",
                        "spotlight@2x" : ""
                    },
                    "iphone" : {
                        "app@2x" : "",
                        "app@3x" : "",
                        "notification@2x" : "",
                        "notification@3x" : "",
                        "settings@2x" : "",
                        "settings@3x" : "",
                        "spotlight@2x" : "",
                        "spotlight@3x" : ""
                    }
                }
            },
            "splashscreen" : {
                "androidStyle" : "default",
                "iosStyle" : "common",
                "android" : {
                    "hdpi" : "static/first.png",
                    "xhdpi" : "static/first.png",
                    "xxhdpi" : "static/first.png"
                },
                "useOriginalMsgbox" : false
            }
        },
        "nativePlugins" : {
            "TUICallingUniPlugin-TUICallingModule" : {
                "__plugin_info__" : {
                    "name" : "TUICallingUniPlugin-TUICallingModule",
                    "description" : "腾讯云音视频插件",
                    "platforms" : "Android,iOS",
                    "url" : "",
                    "android_package_name" : "",
                    "ios_bundle_id" : "",
                    "isCloud" : false,
                    "bought" : -1,
                    "pid" : "",
                    "parameters" : {}
                }
            }
        }
    },
    /* 快应用特有相关 */
    "quickapp" : {},
    /* 小程序特有相关 */
    "mp-weixin" : {
        "appid" : "wx4d565e763241b510",
        "setting" : {
            "urlCheck" : false
        },
        "usingComponents" : true,
        "optimization" : {
            "subPackages" : true
        }
    },
    "mp-alipay" : {
        "usingComponents" : true
    },
    "mp-baidu" : {
        "usingComponents" : true
    },
    "mp-toutiao" : {
        "usingComponents" : true
    },
    "uniStatistics" : {
        "enable" : false
    },
    "vueVersion" : "3",
    "h5" : {
        "sdkConfigs" : {
            "maps" : {
                "qqmap" : {
                    "key" : "3U7BZ-AZZKD-IXC4C-HZA7T-2PGKT-EZFER"
                }
            }
        }
    }
}
