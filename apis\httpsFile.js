const requestFile = (_url, data, args) => {
	return new Promise((resolve, reject) => {
		const {
			method = 'POST',
			header,
			isLoad = true
		} = args
		let isShowLoading = false;
		if(isLoad){
				uni.showLoading({
				title: '加载中...',
				mask: true
			})
			isShowLoading = true;
		}
		// const url = baseUrl + '/api/' + _url
		const url = _url
		uni.request({
			url,
			method,
			data,
			header,
			responseType: 'blob',
			success: (res) => {
				const { data } = res
				 console.log(res)
				isShowLoading&&uni.hideLoading()
				try {
				  if(res){
					const elink = document.createElement('a');
					elink.download = '工资表.xls';
					elink.style.display = 'none';
					const blob = new Blob([res.data], { type: 'application/vnd.ms-excel' });
					elink.href = URL.createObjectURL(blob);
					document.body.appendChild(elink);
					elink.click();
					document.body.removeChild(elink);
				  }else {
					this.$message.error('导出异常请联系管理员');
				  }
				} catch (error) {
				  console.log(error);
				  this.$message.error('导出异常请联系管理员');
				}
			},
			complete: err => {
				console.log(url, data, err)
				isShowLoading&&uni.hideLoading()
			}
		})
	})
	
}

export default requestFile