<template>
	<view class="work_1" v-if="current==0">
		<!-- <zSwiper
			:autoplay='autoplay'
			:list='listAdv'
			:effect3d='effect3d' 
			:fullScreen='fullScreen' 
			:effect3dMargin='`${effect3dMargin}rpx`' 
			:vertical='vertical'
			:topFloat='topFloat'
			:fotterFloat='fotterFloat'
			:mode='mode'
			:indicatorPos='indicatorPos'
			></zSwiper> -->
		<view class="vedioContent">
			<ml-swiper
			  :videoList="listAdv" 
			  :width="width"
			  :height="height"
			  :bottomStyle="bottomStyle"
			  @loadMore="loadMore" 
			  @change="onchange" 
			  @play="play" 
			  @pause="pause" 
			  @ended="ended"
			  @error="error" 
			  @waiting="waiting" 
			  @videoClick="videoClick" 
			  @doubleClick="doubleClick" 
			  @maskClick="maskClick">
			  <!-- 使用 bottom 插槽，定义底部 视频标题等 -->
			  <template v-slot:bottom="{ video, index }">
				<!-- 视频标题 -->
				<text class="videoTitle" v-if="video">
				  {{ video?.title }}
				</text>
			  </template>
			</ml-swiper>
		</view>
	</view>
</template>

<script setup>
	import {
		ref,
		toRaw,
		onMounted
	} from 'vue'
	import {
		onLoad,
		onShow,
		onNavigationBarButtonTap
	} from "@dcloudio/uni-app"
	import Https from '@/apis/index.js'
	// import HttpIm from '@/common/request.js'
	import userInfoStore from '@/store/userInfo.js'
	// import zSwiper from '@/components/z-swiper-detail/z-swiper-detail.vue'
	import {
		storeToRefs
	} from 'pinia'
	const store = userInfoStore()
	const {
		user,
		roleID,
		teamID
	} = storeToRefs(store)
	const current=ref(0)
    const tabs= ['大喇叭', '小喇叭']
    const effect3d=true
	const effect3dMargin=40
	const autoplay=false
	const vertical=false
	const fullScreen=true
	const topFloat=true
	const fotterFloat=true
	const mode='round'
	const indicatorPos='bottomCenter'
	let listSmall=ref([])
	let listAdv=ref([])
	
	const dotIndex=0//指示器索引
	const dotFloatIndex=0//位置指示器索引
	
	const ad = ref([])
	let styleName = ref("")
	const groupList=ref([])
	let advId=""
	
	// 视频begin——————————————————————————————————————————————————————————
	const win = uni.getSystemInfoSync();
	  const width = win.windowWidth; // 视频组件宽度，可传可不传
	  const height = win.windowHeight;  // 视频组件高度，可传可不传
	  const bottomStyle = {
		  // "position": "absolute",
		  "top": "1px",
		  // "left": "0",
		  // "display": "flex",
		  // "flex-wrap": "wrap",
		  // "flex-direction": "column"
	  }
	  const realList = ref([]); // 【必须传入，{url:"必填",title:"非必填，建议传入",poster:"预览图，非必填，建议传入",...:"可传入其他自定义参数，插槽中可以拿到"}】
	  const currentVedio = ref(0); // 当前播放的视频的索引
	  let context = null; // 视频上下文对象，用于操作视频组件（暂停、播放、倍速、静音、弹幕等）
	  const counter = ref(0);
	
	  // 视频滑动事件，根据需要，可写可不写，非必须
	  const onchange = (index) => {
	    console.log("onchange-当前索引:", index);
	    currentVedio.value = index;
	  };
	
	  // 是否加载更多，根据需要，可写可不写，非必须
	  const loadMore = (index, size) => {
	    console.log("加载更所视频：", index + " / " + size);
	    // 请求 5 次 后不在请求 
	    if (counter.value > 5) return;
	    // 模拟从后台请求数据，并将请求到的数据追加到列表中
	    getList().forEach((item) => {
	      item.title = realList.value.length + "，" + item.title + item.title + item.title;
	      realList.value.push(item);
	    });
	    counter.value = counter.value + 1;
	  };
	
	  // 视频开始播放，根据需要，可写可不写，非必须
	  const play = (context) => {
	    context = context;
	    console.log("视频开始播放");
	    // uni.showToast({ title: "视频开始播放", icon: "none", mask: false });
	  };
	
	  // 视频暂停播放，根据需要，可写可不写，非必须
	  const pause = (context) => {
	    context = context;
	    console.log("视频暂停播放");
	    // uni.showToast({ title: "视频暂停播放", icon: "none", mask: false });
	  };
	
	  // 视频播放结束，根据需要，可写可不写，非必须
	  const ended = (context) => {
	    context = context;
	    console.log("视频播放结束");
	    // uni.showToast({ title: "视频播放结束", icon: "none", mask: false });
	  };
	
	  // 视频播放出错，根据需要，可写可不写，非必须
	  const error = (context, event) => {
	    context = context;
	    console.error("视频播放出错：", event);
	    // uni.showToast({ title: "视频播放出错", icon: "none", mask: false });
	  };
	
	  // 视频出现缓冲，根据需要，可写可不写，非必须
	  const waiting = (context) => {
	    context = context;
	    console.error("视频出现缓冲");
	    // uni.showToast({ title: "视频出现缓冲", icon: "none", mask: false });
	  };
	
	  // 视频点击事件，根据需要，可写可不写，非必须
	  const videoClick = (context, video) => {
	    context = context;
	    console.log("点击了视频：", video);
	    // uni.showToast({ title: "点击了视频", icon: "none", mask: false });
	  };
	
	  // 视频双击事件，根据需要，可写可不写，非必须
	  const doubleClick = (context, video) => {
	    context = context;
	    console.log("双击了视频：", video);
	    // uni.showToast({ title: "双击了视频", icon: "none", mask: false });
	  };
	
	  // 蒙层点击事件，根据需要，可写可不写，非必须
	  const maskClick = (index, video) => {
	    context = context;
	    console.log("点击了蒙层：", index, video);
	    // uni.showToast({ title: "点击了蒙层", icon: "none", mask: false });
	  };
	
	  /**
	   * 短视频列表
	   */
	  const getList = () => {
	    return [{
	      videoId: realList.value.length + 1,
	      title: "。",
	      poster: "https://i02piccdn.sogoucdn.com/2acf176d90718d73",
	      url: "https://txmov2.a.yximgs.com/upic/2020/11/08/19/BMjAyMDExMDgxOTQxNTlfNTIzNDczMzQ0XzM4OTQ1MDk5MTI4XzFfMw==_b_Bc770a92f0cf153407d60a2eddffeae2a.mp4",
	      uploadTime: "2023-11-08 19:41",
	      ipLocation: "上海",
	      author: {
	        authorId: 101,
	        avatar: "https://i02piccdn.sogoucdn.com/2acf176d90718d73",
	        nickName: "陌路",
	        genderName: "男"
	      }
	    }];
	  }
	// 视频end————————————————————————————————————————————————————————————————————————————
	
	onShow(() => {
		let userInfo = toRaw(user.value)
		if (userInfo.ID == '') {
			store.clear()
			uni.reLaunch({
				url: '/pagesSub/pages/login/login'
			})
		}
		if (roleID.value == '') {
			uni.reLaunch({
				url: '/pages/my/my'
			})
		}
	})
	onLoad((e) => {
		advId=e.aid;
		getAdv();
	})
	onNavigationBarButtonTap(() => {
		uni.navigateTo({
			url: '/pagesSub/pages/message/message'
		})
	})
	onMounted(() => {
		getAdv();
	})
	const handleAdClick=(position)=>{
	                console.log('handleAdClick ' + position)
	            }
	const changeTab=(index)=>{
		// uni.navigateTo({
		// 	url: '/pagesSub/pages/chat/groupInfo/grouplist'
		// })
		console.log(index)
	}
	
	const resetEvent=()=>{
		styleName.value=""
		getGroup();
	}
	const searchEvent=()=>{
		getGroup();
	}
	
	const publishEvent=()=>{
		uni.removeStorageSync('storage_orderinfo');
		
		if(current.value==0){
			uni.navigateTo({
				url: '/pagesSub/pages/adv/publishFH'
			})
		}
		if(current.value==1){
			uni.navigateTo({
				url: '/pagesSub/pages/adv/publishZG'
			})
		}
		if(current.value==2){
			uni.navigateTo({
				url: '/pagesSub/pages/adv/publishSH'
			})
		}
	}
	
	const getGroup = async() => {
		let r= await Https.Chat.GroupList({
			groupId:'',
			groupName:styleName.value
		})
		console.log(r)
		groupList.value.splice(0)
		if(r.ResData.length>0){
			r.ResData.forEach(function(item,index){
				groupList.value.push(item)
			})
		}
		
		// console.log(groupList.value)
	}
	const getAdv =async()=>{
		console.log(advId)
		listAdv.value.splice(0)
		let userInfo = toRaw(user.value)
		console.log(userInfo.ID)
		Https.Adv.AdvDetial({
			id:advId.toString() 
		}).then(function(res){
			 console.log(res)
			 if(res.Success&&res.ResData.length>0){
				 var item = res.ResData[0];
				 if(item.category=="big"){
					 
				  }
				  if(item.category=="small"){
				  	 
				  }
				  if(listAdv.value.length==0){
						 if(item.type=="SH"){
							 listAdv.value.push({
								videoId: listAdv.value.length+1,
								title: "接货消息:\n\t地址:"+item.sh_address+"\n\t"+
								"姓名:"+item.sh_name+"\n\t"+
								"联系电话:"+item.sh_tel+"\n\t"+
								"人数规模:"+item.sh_gm+"人 \n\t"+
								"擅长款式:"+item.sh_style+"\n\t"+
								"包料能力:"+item.sh_blnl+"\n\t"+
								"打版开发能力:"+item.sh_dbkfnl+"\n\t"+
								"加工难度:"+item.sh_jgnd+"\n\t",
								poster: "https://img1.baidu.com/it/u=1314619043,374926406&fm=253&fmt=auto?w=348&h=500",//'https://i02piccdn.sogoucdn.com/2acf176d90718d73",
								url: Https.Url.baseUrlBiz+"/"+item.sh_mat_path,
								uploadTime: "2024-10-02 09:41",
								ipLocation: "北京",
								author: {
								  authorId: 102,
								  avatar: "https://i02piccdn.sogoucdn.com/2acf176d90718d73",
								  nickName: "管理员",
								  genderName: "女"
								}
							 })
						 }
						 if(item.type=="FH"){
								 listAdv.value.push({
									videoId: listAdv.value.length+1,
									title: "发货消息:\n\t"+
									"联系电话:"+item.fh_Tel+"\n\t"+
									"期望加工地址:"+item.fh_address+"\n\t"+
									"工期限制:"+item.fh_limitGQ+"\n\t"+
									"基本服装类型:"+item.fh_fzType+"\n\t"+
									"基本工艺标准:"+item.fh_JBGYBZ+"\n\t"+
									"账期期望:"+item.fh_ZQQW+"\n\t"+
									"订单数量:"+item.fh_orderNum+"\n\t"+
									"是否包裁:"+item.fh_SFBC+"\n\t"+
									"是否包面辅料:"+item.fh_SFMLFZ+"\n\t",
									poster: "https://img1.baidu.com/it/u=1314619043,374926406&fm=253&fmt=auto?w=348&h=500",//'https://i02piccdn.sogoucdn.com/2acf176d90718d73",
									url: Https.Url.baseUrlBiz+"/"+item.fh_mat_path,
									uploadTime: "2024-10-02 09:41",
									ipLocation: "北京",
									author: {
									  authorId: 102,
									  avatar: "https://i02piccdn.sogoucdn.com/2acf176d90718d73",
									  nickName: "管理员",
									  genderName: "女"
									}
							})
						 }
						 if(item.type=="ZG"){
							var zhaopinList =JSON.parse(item.zg_zaopinList);
							var gwList = ""
							zhaopinList.forEach(function(item,index){
								 gwList+=item.type+"  "+"招聘人数:"+item.quantity+"人\n\t"
							});
							console.log(zhaopinList)
							listAdv.value.push({
								videoId:  listAdv.value.length+1,
								title: "招聘通知:\n\t地址:"+item.zg_address+"\n\t"+
								"姓名:"+item.zg_name+"\n\t"+
								"联系电话:"+item.zg_tel+"\n\t"+
								"招聘信息:"+"\n\t"+gwList,
								poster: "https://img1.baidu.com/it/u=1314619043,374926406&fm=253&fmt=auto?w=348&h=500",//'https://i02piccdn.sogoucdn.com/2acf176d90718d73",
								url: Https.Url.baseUrlBiz+"/"+item.zg_mat_path,
								uploadTime: "2024-10-02 09:41",
								ipLocation: "北京",
								author: {
								  authorId: 102,
								  avatar: "https://i02piccdn.sogoucdn.com/2acf176d90718d73",
								  nickName: "管理员",
								  genderName: "女"
								}
							 })
						 }
				  }
			 }
			console.log(listAdv.value)
		}.bind(this));
	}
</script>

<style lang="scss" scoped>
	.imgV{
		width: 100%;
		text-align: center;
		min-height: 65vh;
	}
	.work{
			background-color: $uni-bg-color;
			margin: 10rpx;
			border-radius: $uni-border-radius-lg;
		
			.title {
				display: flex;
				align-items: center;
				height: 80rpx;
				padding: 0 20rpx;
				border-bottom: 1px solid $uni-border-color;
			}
		
			.menu {
				display: grid;
				grid-template-columns: repeat(3, minmax(0, 1fr));
				gap: 30rpx;
				padding: 30rpx 0;
				font-size: 24rpx;
				text-align: center;
		
				.item {
					color: $uni-text-color-grey;
					
					.img {
						// background-color: $uni-bg-color-grey;
						height: 128rpx;
						width: 128rpx;
						border-radius: $uni-border-radius-lg;
						margin: 0 auto 10rpx;
					}
				}
			}
		
			.menu_1 {
				display: grid;
				grid-template-columns: repeat(4, minmax(0, 1fr));
				gap: 30rpx;
				padding: 30rpx 0;
				font-size: 24rpx;
				text-align: center;
		
				.item {
					color: $uni-text-color-grey;
					text-align: center;
		
					.img {
						// background-color: $uni-bg-color-grey;
						height: 100rpx;
						width: 100rpx;
						border-radius: $uni-border-radius-lg;
						margin: 10rpx 10rpx 20rpx 10rpx;
					}
				}
			}
	}
	
	.search {
		display: grid;
		grid-template-columns: repeat(1, minmax(0, 1fr));
		padding: 20rpx;
		gap: 20rpx;
		background-color: $uni-bg-color;
		.item {
			display: flex;
			align-items: center;
			gap: 20rpx;
			font-size: 28rpx;
			.label {
				flex-shrink: 0;
			}
			.input {
				flex: 1;
				background-color: $uni-bg-color-grey;
				border-radius: 10rpx;
				padding: 0 20rpx;
				min-height: 60rpx;
				.value {
					display: flex;
					align-items: center;
					justify-content: space-between;
					min-height: 60rpx;
					&::after {
						font-family: 'iconfont';
						content: '\e840';
						color: $uni-text-color-grey;
					}
					&:empty::before {
						content: '请选择';
						color: gray;
					}
				}
			}
		}
		.submit {
			background-color: $uni-color-primary;
			color: $uni-text-color-inverse;
		}
		.btnGroup{
			 width: 100rpx !important;
		}
	}
	
	.submit_deploy {
		position: fixed;
		left: 20rpx;
		right: 20rpx;
		// bottom: calc(constant(safe-area-inset-bottom) + 20rpx);
		// bottom: calc(env(safe-area-inset-bottom) + 20rpx);
		bottom: calc(env(safe-area-inset-bottom) + 120rpx);
		background-color: $uni-color-primary;
		color: $uni-text-color-inverse;
		height: 80rpx;
		bottom: 10rpx;
	}
	  .person-head {
	        position: relative;
	        background-color: #fff;
	        margin-left: 20rpx;
	        margin-right: 20rpx;
	    }
		// 视频begin ————————————————————————————————————————
		
		.uniui-right {
		    /* #ifndef APP-NVUE */
		    display: grid;
		    text-align: center;
		    margin: 0 auto;
		    /* #endif */
		    justify-content: center;
		  }
		
		  .videoTitle {
		    padding: 5px;
		    color: #de4a00;
		    font-size: 13px;
		    lines: 13;
		    white-space: normal;
			
			// position: absolute;
			// bottom: -10px;
			// left: 0;
			// /* #ifndef APP-NVUE */
			// display: flex;
			// /* #endif */
			// flex-wrap: wrap;
			// flex-direction: column;
		  }
		
		  .userAvatar {
		    width: 35px;
		    height: 35px;
		    border-radius: 100px;
		    margin-bottom: 10px;
		    border: 1rpx solid #fff;
		    background-color: #fafafa;
		  }
		
		  .iconTitle {
		    font-size: 12px;
		    color: #fff;
		    text-align: center;
		    padding-bottom: 5px;
		  }
		// 视频end————————————————————————————————————————————
</style>