<template>
	<view class="modal" :class="[{ show }, {'bottom': bottom}]" @click="handleClose" :style="`z-index: ${zIndex}`">
		<view class="dialog" @click.stop>
			<slot></slot>
		</view>
	</view>
</template>

<script setup>
const emits = defineEmits(['close'])
const props = defineProps({
	show: <PERSON><PERSON><PERSON>,
	bottom: Boolean,
	zIndex: '99999'
})

const handleClose = () => {
	emits('close')
}
</script>

<style lang="scss" scoped>
.modal {
	position: fixed;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	opacity: 0;
	outline: 0;
	text-align: center;
	transform: scale(1.185);
	backface-visibility: hidden;
	perspective: 2000rpx;
	background: rgba(0, 0, 0, 0.6);
	transition: all 0.3s ease-in-out 0s;
	pointer-events: none;
	&::before {
		content: '\200B';
		display: inline-block;
		height: 100%;
		vertical-align: middle;
	}
	&.show {
		opacity: 1;
		transition-duration: 0.3s;
		transform: scale(1);
		overflow-x: hidden;
		overflow-y: auto;
		pointer-events: auto;
	}
	.dialog {
		position: relative;
		display: inline-block;
		vertical-align: middle;
		margin-left: auto;
		margin-right: auto;
		width: 680rpx;
		max-width: 100%;
		background-color: #f8f8f8;
		border-radius: 10rpx;
		overflow: hidden;
	}
	&.bottom {
		margin-bottom: -1000rpx;
		&::before {
			vertical-align: bottom;
		}
		&.show {
			margin-bottom: 0;
		}
		.dialog {
			width: 100%;
			border-radius: 0;
		}
	}
}
</style>