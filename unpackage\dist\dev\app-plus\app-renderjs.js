var __renderjsModules={};

__renderjsModules["5a1e922e"] = (() => {
  var __defProp = Object.defineProperty;
  var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
  var __getOwnPropNames = Object.getOwnPropertyNames;
  var __hasOwnProp = Object.prototype.hasOwnProperty;
  var __export = (target, all) => {
    for (var name in all)
      __defProp(target, name, { get: all[name], enumerable: true });
  };
  var __copyProps = (to, from, except, desc) => {
    if (from && typeof from === "object" || typeof from === "function") {
      for (let key of __getOwnPropNames(from))
        if (!__hasOwnProp.call(to, key) && key !== except)
          __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
    }
    return to;
  };
  var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

  // <stdin>
  var stdin_exports = {};
  __export(stdin_exports, {
    default: () => stdin_default
  });

  // ../../../Users/<USER>/Desktop/打印机/App源码/App/uni_modules/uni-swipe-action/components/uni-swipe-action-item/render.js
  var MIN_DISTANCE = 10;
  var render_default = {
    showWatch(newVal, oldVal, ownerInstance, instance, self) {
      var state = self.state;
      var $el = ownerInstance.$el || ownerInstance.$vm && ownerInstance.$vm.$el;
      if (!$el)
        return;
      this.getDom(instance, ownerInstance, self);
      if (newVal && newVal !== "none") {
        this.openState(newVal, instance, ownerInstance, self);
        return;
      }
      if (state.left) {
        this.openState("none", instance, ownerInstance, self);
      }
      this.resetTouchStatus(instance, self);
    },
    /**
     * 开始触摸操作
     * @param {Object} e
     * @param {Object} ins
     */
    touchstart(e, ownerInstance, self) {
      let instance = e.instance;
      let disabled = instance.getDataset().disabled;
      let state = self.state;
      this.getDom(instance, ownerInstance, self);
      disabled = this.getDisabledType(disabled);
      if (disabled)
        return;
      instance.requestAnimationFrame(function() {
        instance.removeClass("ani");
        ownerInstance.callMethod("closeSwipe");
      });
      state.x = state.left || 0;
      this.stopTouchStart(e, ownerInstance, self);
    },
    /**
     * 开始滑动操作
     * @param {Object} e
     * @param {Object} ownerInstance
     */
    touchmove(e, ownerInstance, self) {
      let instance = e.instance;
      if (!instance)
        return;
      let disabled = instance.getDataset().disabled;
      let state = self.state;
      disabled = this.getDisabledType(disabled);
      if (disabled)
        return;
      this.stopTouchMove(e, self);
      if (state.direction !== "horizontal") {
        return;
      }
      if (e.preventDefault) {
        e.preventDefault();
      }
      let x = state.x + state.deltaX;
      this.move(x, instance, ownerInstance, self);
    },
    /**
     * 结束触摸操作
     * @param {Object} e
     * @param {Object} ownerInstance
     */
    touchend(e, ownerInstance, self) {
      let instance = e.instance;
      let disabled = instance.getDataset().disabled;
      let state = self.state;
      disabled = this.getDisabledType(disabled);
      if (disabled)
        return;
      this.moveDirection(state.left, instance, ownerInstance, self);
    },
    /**
     * 设置移动距离
     * @param {Object} value
     * @param {Object} instance
     * @param {Object} ownerInstance
     */
    move(value, instance, ownerInstance, self) {
      value = value || 0;
      let state = self.state;
      let leftWidth = state.leftWidth;
      let rightWidth = state.rightWidth;
      state.left = this.range(value, -rightWidth, leftWidth);
      instance.requestAnimationFrame(function() {
        instance.setStyle({
          transform: "translateX(" + state.left + "px)",
          "-webkit-transform": "translateX(" + state.left + "px)"
        });
      });
    },
    /**
     * 获取元素信息
     * @param {Object} instance
     * @param {Object} ownerInstance
     */
    getDom(instance, ownerInstance, self) {
      var state = self.state;
      var $el = ownerInstance.$el || ownerInstance.$vm && ownerInstance.$vm.$el;
      var leftDom = $el.querySelector(".button-group--left");
      var rightDom = $el.querySelector(".button-group--right");
      state.leftWidth = leftDom.offsetWidth || 0;
      state.rightWidth = rightDom.offsetWidth || 0;
      state.threshold = instance.getDataset().threshold;
    },
    getDisabledType(value) {
      return (typeof value === "string" ? JSON.parse(value) : value) || false;
    },
    /**
     * 获取范围
     * @param {Object} num
     * @param {Object} min
     * @param {Object} max
     */
    range(num, min, max) {
      return Math.min(Math.max(num, min), max);
    },
    /**
     * 移动方向判断
     * @param {Object} left
     * @param {Object} value
     * @param {Object} ownerInstance
     * @param {Object} ins
     */
    moveDirection(left, ins, ownerInstance, self) {
      var state = self.state;
      var threshold = state.threshold;
      var position = state.position;
      var isopen = state.isopen || "none";
      var leftWidth = state.leftWidth;
      var rightWidth = state.rightWidth;
      if (state.deltaX === 0) {
        this.openState("none", ins, ownerInstance, self);
        return;
      }
      if (isopen === "none" && rightWidth > 0 && -left > threshold || isopen !== "none" && rightWidth > 0 && rightWidth + left < threshold) {
        this.openState("right", ins, ownerInstance, self);
      } else if (isopen === "none" && leftWidth > 0 && left > threshold || isopen !== "none" && leftWidth > 0 && leftWidth - left < threshold) {
        this.openState("left", ins, ownerInstance, self);
      } else {
        this.openState("none", ins, ownerInstance, self);
      }
    },
    /**
     * 开启状态
     * @param {Boolean} type
     * @param {Object} ins
     * @param {Object} ownerInstance
     */
    openState(type, ins, ownerInstance, self) {
      let state = self.state;
      let leftWidth = state.leftWidth;
      let rightWidth = state.rightWidth;
      let left = "";
      state.isopen = state.isopen ? state.isopen : "none";
      switch (type) {
        case "left":
          left = leftWidth;
          break;
        case "right":
          left = -rightWidth;
          break;
        default:
          left = 0;
      }
      if (state.isopen !== type) {
        state.throttle = true;
        ownerInstance.callMethod("change", {
          open: type
        });
      }
      state.isopen = type;
      ins.requestAnimationFrame(() => {
        ins.addClass("ani");
        this.move(left, ins, ownerInstance, self);
      });
    },
    getDirection(x, y) {
      if (x > y && x > MIN_DISTANCE) {
        return "horizontal";
      }
      if (y > x && y > MIN_DISTANCE) {
        return "vertical";
      }
      return "";
    },
    /**
     * 重置滑动状态
     * @param {Object} event
     */
    resetTouchStatus(instance, self) {
      let state = self.state;
      state.direction = "";
      state.deltaX = 0;
      state.deltaY = 0;
      state.offsetX = 0;
      state.offsetY = 0;
    },
    /**
     * 设置滑动开始位置
     * @param {Object} event
     */
    stopTouchStart(event, ownerInstance, self) {
      let instance = event.instance;
      let state = self.state;
      this.resetTouchStatus(instance, self);
      var touch = event.touches[0];
      state.startX = touch.clientX;
      state.startY = touch.clientY;
    },
    /**
     * 滑动中，是否禁止打开
     * @param {Object} event
     */
    stopTouchMove(event, self) {
      let instance = event.instance;
      let state = self.state;
      let touch = event.touches[0];
      state.deltaX = touch.clientX - state.startX;
      state.deltaY = touch.clientY - state.startY;
      state.offsetY = Math.abs(state.deltaY);
      state.offsetX = Math.abs(state.deltaX);
      state.direction = state.direction || this.getDirection(state.offsetX, state.offsetY);
    }
  };

  // <stdin>
  var stdin_default = {
    mounted(e, ins, owner) {
      this.state = {};
    },
    methods: {
      showWatch(newVal, oldVal, ownerInstance, instance) {
        render_default.showWatch(newVal, oldVal, ownerInstance, instance, this);
      },
      touchstart(e, ownerInstance) {
        render_default.touchstart(e, ownerInstance, this);
      },
      touchmove(e, ownerInstance) {
        render_default.touchmove(e, ownerInstance, this);
      },
      touchend(e, ownerInstance) {
        render_default.touchend(e, ownerInstance, this);
      }
    }
  };
  return __toCommonJS(stdin_exports);
})();


__renderjsModules["2651fd3c"] = (() => {
  var __defProp = Object.defineProperty;
  var __defProps = Object.defineProperties;
  var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
  var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
  var __getOwnPropNames = Object.getOwnPropertyNames;
  var __getOwnPropSymbols = Object.getOwnPropertySymbols;
  var __hasOwnProp = Object.prototype.hasOwnProperty;
  var __propIsEnum = Object.prototype.propertyIsEnumerable;
  var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
  var __spreadValues = (a, b) => {
    for (var prop in b || (b = {}))
      if (__hasOwnProp.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    if (__getOwnPropSymbols)
      for (var prop of __getOwnPropSymbols(b)) {
        if (__propIsEnum.call(b, prop))
          __defNormalProp(a, prop, b[prop]);
      }
    return a;
  };
  var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
  var __export = (target, all) => {
    for (var name in all)
      __defProp(target, name, { get: all[name], enumerable: true });
  };
  var __copyProps = (to, from, except, desc) => {
    if (from && typeof from === "object" || typeof from === "function") {
      for (let key of __getOwnPropNames(from))
        if (!__hasOwnProp.call(to, key) && key !== except)
          __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
    }
    return to;
  };
  var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
  var __async = (__this, __arguments, generator) => {
    return new Promise((resolve, reject) => {
      var fulfilled = (value) => {
        try {
          step(generator.next(value));
        } catch (e) {
          reject(e);
        }
      };
      var rejected = (value) => {
        try {
          step(generator.throw(value));
        } catch (e) {
          reject(e);
        }
      };
      var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
      step((generator = generator.apply(__this, __arguments)).next());
    });
  };

  // <stdin>
  var stdin_exports = {};
  __export(stdin_exports, {
    default: () => stdin_default
  });

  // ../../../Users/<USER>/Desktop/打印机/App源码/App/uni_modules/xe-upload/tools/tools.js
  var awaitWrap = (promise) => promise.then((res) => [null, res]).catch((err) => [err, {}]);
  var fileToBase64 = (file) => {
    if (!file)
      return;
    return new Promise((r, j) => {
      const reader = new FileReader();
      reader.onloadend = () => {
        const base64String = reader.result;
        r(base64String);
      };
      reader.onerror = () => {
        j({ mode: "fileToBase64", data: { errMsg: "File to base64 fail." } });
      };
      reader.readAsDataURL(file);
    });
  };

  // ../../../Users/<USER>/Desktop/打印机/App源码/App/uni_modules/xe-upload/tools/apis.js
  var appUploadFile = (config, exts = {}, onprogress) => {
    const { url, header, formData } = config;
    return new Promise((r, j) => {
      const xhr = new XMLHttpRequest();
      xhr.open("POST", url, true);
      for (let key in header) {
        xhr.setRequestHeader(key, header[key]);
      }
      if (onprogress) {
        xhr.upload.onprogress = onprogress;
      }
      xhr.onreadystatechange = function() {
        if (xhr.readyState === 4) {
          if (xhr.status === 200) {
            r(__spreadProps(__spreadValues({}, exts), { response: JSON.parse(xhr.responseText) }));
          } else {
            j({ mode: "uploadFile", data: { data: xhr.responseText, errMsg: "uploadFile fail." } });
          }
        }
      };
      xhr.send(formData);
    });
  };

  // <stdin>
  var stdin_default = {
    data() {
      return {
        id: 0,
        // 上传框ID
        uploadOptions: {}
        // 上传配置
      };
    },
    methods: {
      // 处理 XeUpload 传入 renderjs 数据，以及调起上传框
      renderProps(info) {
        const { id, renderInput, upload } = info;
        if (!renderInput)
          return;
        this.id = id;
        this.uploadOptions = upload;
        this.$nextTick(() => {
          var _a;
          const dom = document.getElementById(`xe-upload-${id}`);
          dom.addEventListener("change", () => {
            this.handleUpload();
          });
          (_a = dom == null ? void 0 : dom.click) == null ? void 0 : _a.call(dom);
        });
      },
      // 处理文件上传(没有传入url时返回本地链接)
      handleUpload() {
        return __async(this, null, function* () {
          const {
            url,
            name,
            header = {},
            formData = {}
          } = this.uploadOptions || {};
          const dom = document.getElementById(`xe-upload-${this.id}`);
          if (!dom.files[0])
            return;
          const tmpFileList = Array.from(dom.files);
          const tmpUploads = [];
          for (let i = 0; i < tmpFileList.length; i += 1) {
            const e = tmpFileList[i];
            let tmpType = "file";
            if (e.type.includes("image")) {
              tmpType = "image";
            }
            if (e.type.includes("video")) {
              tmpType = "video";
            }
            const tmpExts = {
              size: e.size,
              name: e.name,
              type: e.type,
              fileType: tmpType,
              tempFilePath: "",
              base64Url: ""
            };
            if (!url) {
              const [parseError, parseUrl] = yield awaitWrap(fileToBase64(dom.files[i]));
              if (!parseError) {
                tmpExts.base64Url = parseUrl;
              }
              tmpUploads.push(tmpExts);
              continue;
            }
            ;
            const tmpData = new FormData();
            tmpData.append(name, dom.files[i], e.name);
            for (let key in formData) {
              tmpData.append(key, formData[key]);
            }
            const onprogress = (ev) => {
              if (ev.lengthComputable) {
                var result = ev.loaded / ev.total * 100;
                this.handleRenderEmits({
                  type: "onprogress",
                  data: {
                    progress: Math.floor(result),
                    current: i + 1,
                    total: tmpFileList.length
                  }
                });
              }
              ;
            };
            tmpUploads.push(appUploadFile({
              url,
              header,
              formData: tmpData
            }, tmpExts, onprogress));
          }
          if (!url) {
            return this.handleRenderEmits({
              type: "choose",
              data: tmpUploads
            });
          }
          this.handleRenderEmits({
            type: "onprogress",
            data: {
              progress: 0,
              current: 1,
              total: tmpFileList.length
            }
          });
          const [err, res] = yield awaitWrap(Promise.all(tmpUploads));
          if (err) {
            return this.handleRenderEmits({
              type: "warning",
              data: err
            });
          }
          this.handleRenderEmits({
            type: "success",
            data: res
          });
        });
      },
      // 数据传输到XeUpload组件
      handleRenderEmits(data) {
        this.$ownerInstance.callMethod("handleEmits", data);
      }
    }
  };
  return __toCommonJS(stdin_exports);
})();
