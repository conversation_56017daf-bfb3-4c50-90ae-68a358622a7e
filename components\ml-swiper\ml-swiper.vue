<template>
  <view class="ml-swiper-view">
	  
    <!-- #ifdef H5 -->
    <swiper v-if="realList && realList.length > 0" class="ml-swiper" :video="true" :current="current" :circular="false"
      :vertical="true" :skip-hidden-item-layout="true" :style="fullScreen" @change="onchange">
      <swiper-item class="ml-swiper-item" v-for="(video, index) in realList" :key="index">
        <cover-view class="ml-swiper-item-view-box" :style="fullScreen">
          <view class="ml-swiper-item-video" @click="h5VideoClick(video, index)" v-if="current === index"
            :style="fullScreen" v-html='`<video id="${h5IdPrefix + index}" class="ml-swiper-item-video" src="${video.url}" poster="${video.poster}" 
                title="${video.title}" autoplay="${videoOptions.autoplay}" loop="${videoOptions.loop}" muted="${videoOptions.muted}"
                preload="auto" objectFit="${videoOptions.objectFit}" x5-video-orientation="portrait" enable-progress-gesture="false"
                webkit-playsinline="true" playsinline="true" x-webkit-airplay="allow" x5-video-player-type="h5-page" style="width: ${videoOptions.width}px;
                height: ${videoOptions.height}px;object-fit: ${videoOptions.objectFit};pointer-events: auto !important;background: #000;"></video>`'>
          </view>
          <cover-view class="ml-swiper-item-view-right-box" :style="rightStyle">
            <slot name="right" :video="video" :index="index"></slot>
          </cover-view>
          <cover-view class="ml-swiper-item-view-bottom-box" :style="bottomStyle">
            <slot name="bottom" :video="video" :index="index"></slot>
          </cover-view>
          <image v-if="current !== index && video.poster" :src="video.poster" mode="aspectFit"
            :style="[fullScreen, {objectFit: 'contain'}]"></image>
          <cover-view class="center-play-mask" v-if="!playing || loading" :style="fullScreen"
            @click.stop="playVideo(video, index)">
            <view class="center-play-mask-view">
              <cover-image v-if="!loading || current !== index"
                src="data:image/png;base64,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"
                class="center-play-btn">
              </cover-image>
              <cover-image v-if="loading && current === index"
                src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAAAXNSR0IArs4c6QAABX9JREFUaEPtWVuME2UU/s5MFwFFCRh4gMJ2irLMbNspm0B0o/IgoIaoGORBMQZFY6IRlPhiRMVLiAleow9GhA1GgkYMvmi8BWIIKAE63e502Ww7Xa4KSiSCLnbbOTKlXdo67XR70Uqcx3/O+c73/ef/z38j1Pj5+8NTRdG1mBmnNK+ypRhuXiIx+rT5+zoQ3QtgCMAmTVKeqTHssDvVAjQ71tthCua3AK7K4miDSfHGvra2Mzlc1dA/ALAsPw4Db4UlZWUtsXO+NQlQDX3gfI9OLyTCGzSp/SGrzRfv9YlkdtsR5RbX1LB75rFaRVQtoKM/4k2LQsyGgKFJitdqV+P6PSB8aEeSmOaHvPI31r+ZBw+OG9uSWsSC4AbSezWPb2elwqoXcHzf2PS50b8ANKYwGH2lSfJCq609FnG7BOGwDZmzoKRb8wRPB/p1hQT+EkRTcnYMbAtLypJKRDgKyPbiUgZiLoFe3d8q/5gDDhrR+xi8OS/QbybEOd1SW9/wHEhEV4H59QIyhOWaR+my2gKG3ktAWzFZBq0JS/JLTiLKCggY+lICPsqbMJGQpPjzQYMD+q1gWmFVIVMQ1nW3tiWKg6oJ/Q4wloGQBFOXJslfWzZ+Q79BAL4rQXJ4KJYTUVaAakTDABcQBmhBjoBT7zj9D8ajNzNxRozNd1yTlOFhVQrLQYB+AEAw3zl/8jkRdPo/t7//yj/FpFWJrvibLaNL8yrLnTCcBNwN4ONyQ8gpgNP/wEB0MZn8aZHdobSYDkam+3918necxAFDX0bAErtJ7ARe6f/sgrgWwGxi2vzHkPCytRh29PVdnXalFkKgKTDRo3nlz4sxHQVUSqLedrMT+i0mY2veKg8CIkMw5/dIvhN5o6LeoWvHU47oE1qG0A9ggg3aJ5qkWEM78w1n4EJJo7fB5kSQ8LwmyRtqp1IdQtDQVzOwvpR3yjSn9czwHSkQULyvyTeqjkb1XqoRfRvgR0uXTropJMmZ9SOTATXW0wmBdhU4ED2heeQ3qqdRvWe2cFi7WNsvOYhxUUU5ezEDzIKaiFobM0/WwySX6A5NaztePY3qPeft2OE63TpJA0MpRmHGC2Gv8lzpOQCeDPAaTWp/r3oKtXv6DnVLYlq09lmdF9HoHU2SH8tHb9oymiPpT+hzBZM6qEXYbjciml5ATohqROeDuZNAJ1KgXRHvrEhBFao96Y1DUI1oF8D3F0V4SpOU9U2fgUBcX0uEZ227h3BnRoA/Fp4ExqjuawJHG9ePI0duNyKTXRB+KunJfIwKdoN5J6WRh6u/R4mdauFypRq6dYJqzbaeTA7CHVWUZP3pjBwxU4EY35fxNElN6DEwMrcIAH4eT2On7fR4zo08XGM81LiugyDboTPzVgrEI3cRCdssA2Y8EPYqmxpDpTrUzK2FiB8AXF6EcDgtptWmr0IW6ayIjQDmZEQwtqfIfMQ6F/wnBJTL3f8CqhvZ9fO6tDJgXSMS6GTu0rV+/dQ4pOEMBAz9TQIet0IRaEVIkt9vXNj6IecJ6NlHoI6MAMLGkEd5sH5hGoeUL+BhAr2bCWWandoM3+7Gha0fcsEktg73gsBnDkg+21eV+oWtHGnO0d6JQylcG2qdtcfO69KqQpX3S/NYXvoZsE5FLRAXjW4Zt2WP2z3YPH1/gUnZDMi6PuqyMYgx4D7/ZldwqdosQhyHkGro1iPDeDC+0LzKbY0iblWbvVNnnRopvqOAgBFZQCxcLwrprfs9/oMjDVCpvRrveQUC9eZeLyv1cxRQKdC/ZVezgOAhXTbTeJLN1NPdMwIn/2khNQtQDf0zALcz6MWwJNteQAWO9E2hodRrZjq1ut53T7ULyDxi0yoB6ZWltiDBgd7r2DR3N2KP9RdxLfhyI6cVtQAAAABJRU5ErkJggg=="
                class="center-loading">
              </cover-image>
              <text style="color: #fff;text-align: center;">点击播放</text> 
            </view>
          </cover-view>
        </cover-view>
      </swiper-item>
    </swiper>
    <!-- #endif -->
    <!-- #ifdef MP-WEIXIN -->
    <swiper v-if="realList && realList.length > 0" class="ml-swiper" :current="current" :circular="false"
      :vertical="true" :skip-hidden-item-layout="true" :style="fullScreen" @change="onchange">
    <!-- #endif -->
      <!-- #ifdef APP-NVUE -->
      <swiper v-if="realList && realList.length > 0" class="ml-swiper" :current="current" :circular="false"
        :vertical="true" :skip-hidden-item-layout="true" :style="fullScreen" @touchend="touchEnd" @change="onchange">
      <!-- #endif -->
        <!-- #ifdef APP-NVUE | MP-WEIXIN -->
        <swiper-item class="ml-swiper-item" v-for="(video, index) in realList" :key="index">
          <cover-view class="ml-swiper-item-view-box" :style="fullScreen">
            <ml-player :showPlayer="current === index" :video="video" :videoOptions="videoOptions"
              @videoClick="videoClick" @doubleClick="doubleClick" @play="play" @pause="pause" @waiting="waiting"
              @ended="ended" @error="error">
            </ml-player>
            <cover-view class="ml-swiper-item-view-right-box" :style="rightStyle">
              <slot name="right" :video="video" :index="index"></slot>
            </cover-view>
            <cover-view class="ml-swiper-item-view-bottom-box" :style="bottomStyle">
              <slot name="bottom" :video="video" :index="index"></slot>
            </cover-view>
          </cover-view>
        </swiper-item>
      </swiper>
      <!-- #endif -->
      <!-- #ifdef APP-VUE  -->
      <view :style="[fullScreen]" style="display: flex;width: 100%;height: 100%;margin: 0 auto;align-items: center;justify-content: center;">
        <text style="text-align: center;margin: 0 auto;padding: 50px 0;color: #ff5918;font-size: 15px;font-weight: 800;">
          使用VUE页面开发 APP 存在性能和兼容问题，请仔细阅读 ml-swiper APP 篇文档，并作出调整
        </text>
      </view>
      <!-- #endif -->
  </view>
</template>

<script setup>
  import { computed, onMounted, ref } from 'vue';
  import { onHide, onShow, onUnload } from '@dcloudio/uni-app';

  defineOptions({ name: "ml-swiper" });

  const win = uni.getSystemInfoSync();

  const props = defineProps({
    width: { // 播放器宽度
      type: Number,
      default: 0,
      required: false
    },
    height: { // 播放器高度
      type: Number,
      default: 0,
      required: false
    },
    rightStyle: { // 自定义右侧样式
      type: [Object, String],
      default: {},
      required: false
    },
    bottomStyle: { // 自定义底部样式
      type: [Object, String],
      default: {},
      required: false
    },
    videoList: { // 资源列表
      type: Array,
      default: [],
      required: true
    },
    count: { // 临界点，当资源剩余多少时触发加载更多，默认2条
      type: Number,
      default: 2,
      required: false
    },
    showPlay: { // 页面显示时 是否播放
      type: Boolean,
      default: true,
      required: false
    },
    hidePause: { // 程序进入到后台时 是否暂停播放
      type: Boolean,
      default: true,
      required: false
    }
  });

  const videoOptions = ref({
    width: props.width || win.windowWidth, // 宽度
    height: props.height || win.windowHeight, // 高度
    controls: false, // 不显示控制条
    initialTime: 0, // 初始播放位置，单位为秒（s）
    vslideGesture: false, // 不开启亮度与音量调节手势
    showRate: false, // 不使用倍速功能
    showFit: false, // 不使用视频展示形式功能
    enableClick: true, // 开启单击事件
    enableDblClick: true, // 开启双击双击
    autoplay: true, // 是否自动播放
    showPoster: true, // 是否展示预览图
    loop: true, // 是否循环播放
    muted: false, // 是否静音播放
    objectFit: "contain", // 视频展示样式
  });

  let context = null; // 视频播放器上下文对象
  const current = ref(0); // 当前视频所在的索引位置
  const realList = computed(() => props.videoList || []); // 源数据
  const locked = ref(false); // 加锁，防抖处理
  const playing = ref(false); // 是否正在播放中
  const loading = ref(true); // 是否正在加载中...
  const fullScreen = computed(() => {
    return {
      width: videoOptions.value.width + 'px',
      height: videoOptions.value.height + 'px'
    }
  });
  // #ifdef H5
  let h5PlayInter = 0;
  let h5LastTapTime = 0;
  let h5LastClickTimer = 0;
  let h5IdPrefix = "ml-player-";
  // #endif

  const emits = defineEmits([
    'change', // 视频滑动事件
    'play', // 视频开始播放
    'pause', // 视频暂停播放
    'ended', // 视频播放结束
    'error', // 视频播放出错
    'waiting', // 出视频出现缓冲
    'videoClick', // 视频点击事件
    'doubleClick', // 视频双击事件
    'loadMore', // 加载更多(index, size); // index:当前索引，size:列表长度
    'maskClick' // 蒙层被点击事件
  ]);

  const onchange = (event) => {
    if (locked.value) return;
    locked.value = true;
    setTimeout(() => {
      locked.value = false;
    }, 500);
    const index = event.detail.current;
    customPause();
    // #ifdef H5
    delListener();
    // #endif
    current.value = index;
    initSwiperData();
  };

  const initSwiperData = () => {
    isLoadMore();
    // #ifdef H5
    addListener();
    // #endif
	let rawLen = realList.value.length;
    emits('change', current.value, rawLen);
  };

  const isLoadMore = () => {
    let rawLen = realList.value.length;
    let count = Number(isNaN(props.count) ? 2 : props.count);
    let num = Number(Number(rawLen) - count);
    if (num == current.value) {
      emits('loadMore', current.value, rawLen);
    }
  };

  let lastTouchTime = 0;
  const touchEnd = (_e) => {
    if (Date.now() - lastTouchTime < 200) {
      doubleClick(context, realList.value[current.value]);
      lastTouchTime = 0;
      return;
    }
    lastTouchTime = Date.now();
  };

  const play = (vcontext) => {
    if (vcontext) context = vcontext;
    playing.value = true;
    loading.value = false;
    emits('play', context);
  };

  const pause = (vcontext) => {
    if (vcontext) context = vcontext;
    playing.value = false;
    emits('pause', context);
  };

  const waiting = (vcontext) => {
    if (vcontext) context = vcontext;
    loading.value = true;
    emits('waiting', context);
  };

  const ended = (vcontext) => {
    if (vcontext) context = vcontext;
    emits('ended', context);
  };

  const error = (vcontext, event) => {
    if (vcontext) context = vcontext;
    loading.value = false;
    emits('error', context, event);
  };

  const videoClick = (vcontext, video) => {
    if (vcontext) context = vcontext;
    if (playing.value === true) {
      customPause();
    } else {
      customPlay();
    }
    emits('videoClick', context, video);
  };

  const doubleClick = (vcontext, video) => {
    if (vcontext) context = vcontext;
    emits('doubleClick', context, video);
  };

  const playVideo = (video, index) => {
    // #ifdef H5
    const playerId = h5IdPrefix + index;
    let vcontext = document.querySelector(`#${playerId}`);
    if (vcontext) context = vcontext;
    // #endif
    customPlay();
    emits('maskClick', video, index);
  };

  // #ifdef H5
  const h5VideoClick = (video, index) => {
    const playerId = h5IdPrefix + index;
    let vcontext = document.querySelector(`#${playerId}`);
    if (vcontext) context = vcontext;
    let currentTime = Date.now();
    let lastTime = h5LastTapTime;
    h5LastTapTime = currentTime;
    if (!context) {
      uni.showToast({
        title: "操作失败，刷新重试！",
        icon: "none",
        mask: false
      });
      return;
    }
    playing.value = context?.paused === false;
    if (currentTime - lastTime < 250) {
      clearTimeout(h5LastClickTimer);
      if (videoOptions.value.enableDblClick) {
        doubleClick(context, video);
      }
    } else {
      h5LastClickTimer = setTimeout(() => {
        videoClick(context, video);
      }, 250);
    }
  };

  function h5Play(_event) {
    if (context) {
      play(context);
      loading.value = false;
      clearInterval(h5PlayInter);
    }
  }

  function h5Pause(_event) {
    if (context) {
      pause(context);
    }
  }

  function h5Ended(_event) {
    if (context) {
      ended(context);
    }
  }

  function h5Error(event) {
    error(context, event);
  }

  const addListener = () => {
    clearInterval(h5PlayInter);
    h5PlayInter = setInterval(() => {
      const playerId = h5IdPrefix + current.value;
      let videoElement = document.querySelector(`#${playerId}`);
      if (!videoElement) return;
      try {
        clearInterval(h5PlayInter);
        context = videoElement;
        videoElement?.addEventListener('play', h5Play);
        videoElement?.addEventListener('pause', h5Pause);
        videoElement?.addEventListener('ended', h5Ended);
        videoElement?.addEventListener('error', h5Error);
        playing.value = videoElement?.paused === false;
        videoElement.muted = videoOptions.value.muted;
      } catch (e) {}
    }, 50);
  }

  const delListener = () => {
    if (!context) return;
    try {
      context?.removeEventListener('play', h5Play);
      context?.removeEventListener('pause', h5Pause);
      context?.removeEventListener('ended', h5Ended);
      context?.removeEventListener('error', h5Error);
      clearInterval(h5PlayInter);
      clearTimeout(h5LastClickTimer);
    } catch (e) {}
  };
  // #endif

  function customPause() {
    if (context) {
      try {
        context?.pause();
        playing.value = false;
      } catch (e) {
        console.error(e);
      }
    }
  }

  function customPlay() {
    if (context) {
		console.log(context)
      try {
        context?.play();
        playing.value = true;
        loading.value = false;
      } catch (e) {
        console.error(e);
      }
    }
  }

  function customStop() {
    if (context) {
      try {
        context?.stop();
      } catch (e) {
        console.error(e);
      }
    }
  }

  onShow(() => {
    if (props.showPlay) {
      customPlay();
    }
  });
  
  onMounted(() => {
    // #ifdef H5
    addListener();
    // #endif
  });

  onHide(() => {
    if (props.hidePause) {
      customPause();
    }
  });

  onUnload(() => {
    // #ifdef H5
    clearInterval(h5PlayInter);
    clearTimeout(h5LastClickTimer);
    delListener();
    h5PlayInter = 0
    h5LastTapTime = 0
    h5LastClickTimer = 0
    // #endif
    lastTouchTime = 0;
    customStop();
    current.value = 0;
    locked.value = false;
    playing.value = false;
    loading.value = true;
    context = null
  });
</script>

<style scoped lang="scss">
  .ml-swiper-item-view-box {
    position: relative;
    background-color: rgba(0, 0, 0, 0.7);
  }

  .ml-swiper-item-view-right-box {
    position: absolute;
    bottom: 100px;
    /* #ifndef APP-NVUE */
    display: flex;
    bottom: 15%;
    /* #endif */
    padding: 5px;
    right: 1px;
    flex-wrap: wrap;
    flex-direction: column;
  }

  .ml-swiper-item-view-bottom-box {
    position: absolute;
    bottom: 1px;
    left: 0;
    /* #ifndef APP-NVUE */
    display: flex;
    /* #endif */
    flex-wrap: wrap;
    flex-direction: column;
  }

  .center-play-mask {
    position: fixed;
    top: 0;
    left: 0;
    /* #ifndef APP-NVUE */
    display: flex;
    /* #endif */
    align-items: center;
    justify-content: center;
	.center-play-mask-view{
		// top: 86px !important;
	}
    .center-play-btn {
      width: 140rpx;
      height: 140rpx;
    }

    .center-loading {
      width: 100rpx;
      height: 100rpx;
      /* #ifndef APP-NVUE */
      animation: rotate 2s linear infinite;
      /* #endif */
    }

    /* #ifndef APP-NVUE */
    @keyframes rotate {
      from {
        transform: rotate(0deg);
      }

      to {
        transform: rotate(360deg);
      }
    }
    /* #endif */
  }
  
  .ml-swiper-view {
    background-color: rgba(0, 0, 0, 0.7);
  }

  .ml-swiper {
    background-color: rgba(0, 0, 0, 0.7);
  }

  .ml-swiper-item {
    background-color: rgba(0, 0, 0, 0.7);
  }

  .ml-swiper-item-video {
    background-color: rgba(0, 0, 0, 0.7);
  }
</style>