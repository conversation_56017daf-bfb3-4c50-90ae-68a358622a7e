<template>
	<view class="swipe-out">
		<view class="content" @touchstart="handleStart"
			@touchmove.prevent="handleMove" @touchend="handleEnd"
			:style="`transform: translateX(${translateX});`"
		>
			<slot></slot>
		</view>
		<view class="remove" :style="{width: props.width + 'px'}"
			@click="emits('del', props.index)"
		>删除</view>
	</view>
</template>

<script setup>
import { ref } from 'vue'

const emits = defineEmits(['del'])
const props = defineProps({
	index: Number,
	width: {
		type: Number,
		default: 60
	}
})

const drag = ref(1)
const translateX = ref('0px') 
const startX = ref(0)
const handleStart = ({touches}) => {
	const touch = touches[0]
	startX.value = touch.clientX
}

const handleMove = ({touches}) => {
	const touch = touches[0]
	const disX = startX.value - touch.clientX
	if (disX > props.width / 2) {
		if (disX > props.width) {
			translateX.value = -(props.width + disX / drag.value)+'px'
			drag.value += 0.05
			return
		}
		translateX.value = -disX+'px'
	} else {
		translateX.value = '0px'
	}
}
const handleEnd = ({changedTouches}) => {
	const touch = changedTouches[0]
	const disX = startX.value - touch.clientX
	if (disX > props.width / 2) {
		translateX.value = `-${props.width}px`
		return
	} else {
		translateX.value = '0px'
	}
	drag.value = 2
}
const handleClose = () => {
	translateX.value = '0px'
}
</script>

<style lang="scss" scoped>
.swipe-out {
	position: relative;
	overflow: hidden;
	background-color: $uni-color-error;
	.content {
		flex: 1;
		position: relative;
		z-index: 1;
		padding: 0 20rpx;
		background-color: #fff;
		transition: transform 150ms cubic-bezier(0.4, 0, 0.2, 1);
	}
	.remove {
		position: absolute;
		top: 0;
		right: 0;
		width: 120rpx;
		height: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
		color: #fff;
	}
}
</style>