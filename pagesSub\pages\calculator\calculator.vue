<template>
	<view class="tabs">
		<view class="item" :class="{on: current === 0}" @click="handleTabs($event, 0)">普通</view>
		<view class="item" :class="{on: current === 1}" @click="handleTabs($event, 1)">斜条</view>
		<view class="item" :class="{on: current === 2}" @click="handleTabs($event, 2)">工资</view>
    <view class="block" :style="`transform: translateX(${translateX})`"></view>
	</view>
	
	<view class="tab-content">
		<view class="ordinary" v-if="current === 0">
			<view class="history">{{history}}</view>
			<view class="box">
				<view class="num">
					<view class="item" v-for="item in ordinary" :key="item"
						@click="handleNum(item)"
					>{{item}}</view>
				</view>
			</view>
			<view class="group">
				<view class="reset" @click="handleReset">C</view>
				<view class="result" @click="handleResult">=</view>
			</view>
		</view>
		<view class="oblique" v-else-if="current === 1">
			<form @submit="handleInquiry">
				<view class="form">
					<view class="item">
						<view class="label">布料宽度（米)</view>
						<input class="input" name="clothWidth" type="number" placeholder="布料宽度（米)">
					</view>
					<view class="item">
						<view class="label">斜条长度（米）</view>
						<input class="input" name="barLength" type="number" placeholder="每件衣服所需斜条长度（米）">
					</view>
					<view class="item">
						<view class="label">斜条宽度（米）</view>
						<input class="input" name="barWidth" type="digit" placeholder="斜条宽度（米）">
					</view>
					<view class="item">
						<view class="label">件数</view>
						<input class="input" name="countNum" type="number" placeholder="件数">
					</view>
					<button class="btn submit" form-type="submit">查询</button>
				</view>
			</form>
			<view class="result">
				<div class="title">单根斜条长度（米）</div>
				<view>45度： {{format(inquiry.barLength45)}}</view>
				<view>90度： {{format(inquiry.barLength90)}}</view>
				<view>180度： {{format(inquiry.barLength180)}}</view>
				<div class="title">所需总布料长度（米）</div>
				<view>45度： {{format(inquiry.clothLength45)}}</view>
				<view>90度： {{format(inquiry.clothLength90)}}</view>
				<view>180度： {{format(inquiry.clothLength180)}}</view>
			</view>
		</view>
		<view class="wages" v-else>
			<view class="toggle">
				<view class="money">
					<view class="name">睿币</view>
					<input class="num" type="number" v-model="ruibi" @input="handleCheck($event, 'ruibi')" placeholder="请输入睿币">
				</view>
				<text class="icon icon-a-sorting2"></text>
				<view class="money">
					<view class="name">人民币</view>
					<input class="num" type="number" :value="rmb" @input="handleCheck($event, 'rmb')" placeholder="请输入人民币">
				</view>
			</view>
		</view>
	</view>
	<issue></issue>
</template>

<script setup>
import { ref, computed } from 'vue'
import { isEmpty } from '@/utils/index.js'
import Https from '@/apis/index.js'
import userInfoStore from '@/store/userInfo.js'
import { storeToRefs } from 'pinia'

const store = userInfoStore()
const {
	user, beanRate
} = storeToRefs(store)

const format = (item) => {
	if (item) return Math.floor(item * 100) / 100
	else return ''
}
const current = ref(0)
const translateX = ref('0')
const handleTabs = ({target}, num) => {
	current.value = num
	translateX.value = `${target.offsetLeft}px`
	inquiry.value = {}
}

const history = ref('')
const ordinary = [
	'1', '2', '3', '+',
	'4', '5', '6', '-',
	'7', '8', '9', '*',
	'0', '.', 'Del', '/'
]
const handleNum = (item) => {
	const cur = history.value
	if (item === 'del') {
		history.value = cur.slice(0, cur.length - 1)
	} else if(['+', '-', '*', '/'].includes(item)) {
		const last = cur.substr(-1)
		if (['+', '-', '*', '/'].includes(last)) {
			history.value = cur.slice(0, cur.length - 1)
		}
		history.value += item
	} else {
		history.value += item
	}
}
const handleResult = () => {
	history.value = eval(history.value).toString()
}
const handleReset = () => {
	history.value = ''
}

const inquiry = ref({})
const handleInquiry = async({detail}) => {
	let formData = detail.value
	if (isEmpty(formData.clothWidth)) {
		uni.showToast({
			title: '请输入布料宽度',
			icon: 'none'
		})
		return
	}
	if (isEmpty(formData.barLength)) {
		uni.showToast({
			title: '请输入每件衣服所需斜条长度',
			icon: 'none'
		})
		return
	}
	if (isEmpty(formData.barWidth)) {
		uni.showToast({
			title: '请输入斜条宽度',
			icon: 'none'
		})
		return
	}
	if (isEmpty(formData.countNum)) {
		uni.showToast({
			title: '请输入件数',
			icon: 'none'
		})
		return
	}
	formData.userID = Number(user.value.ID)
	formData.clothWidth = Number(formData.clothWidth)
	formData.barLength = Number(formData.barLength)
	formData.barWidth = Number(formData.barWidth)
	formData.countNum = Number(formData.countNum)
	const { data } = await Https.Computation.Do(formData)
	inquiry.value = data.recomputation
}

const rmb = ref('')
const ruibi = ref('')
const handleCheck = ({detail}, name) => {
	const num = Number(detail.value)
	name === 'ruibi' && (rmb.value = num / beanRate.value)
	name === 'rmb' && (ruibi.value = num / beanRate.value)
}
</script>

<style lang="scss" scoped>
.tabs {
	display: flex;
	align-items: center;
	gap: 20rpx;
	height: 100rpx;
  position: relative;
	font-size: 32rpx;
	.item {
		flex: 1;
		text-align: center;
    transition: color .3s ease-in-out;
		&.on {
			color: $uni-color-primary;
		}
	}
	.block {
		background-color: $uni-color-primary;
		position: absolute;
		left: calc(100vw / 3 / 2 - 30rpx);
		bottom: 0;
		height: 4rpx;
		width: 60rpx;
		transition: transform 300ms ease-in-out;
	}
}
.tab-content {
	height: calc(100vh - 100rpx);
	.ordinary {
		.history {
			display: flex;
			align-items: center;
			height: 100rpx;
			border: 1rpx solid $uni-border-color;
			margin: 20rpx;
			background-color: #fff;
			padding: 0 40rpx;
			font-size: 40rpx;
		}
		.box {
			display: flex;
			align-items: center;
			.num {
				flex: 1;
				display: grid;
				grid-template-columns: repeat(4, minmax(0, 1fr));
				place-items: center;
				gap: 20rpx;
				.item {
					display: flex;
					align-items: center;
					justify-content: center;
					width: 100rpx;
					height: 100rpx;
					background-image: linear-gradient(135deg, #F4F5F6, #fff);
					box-shadow: inset 1px 1px 1px 0px rgba(255, 255, 255, 0.8),
											inset -1px -1px 1px 0px rgba(40, 49, 85, 0.3),
											1px 1px 3px 0px rgba(40, 49, 85, 0.1);
					border-radius: 16rpx;
				}
			}
		}
		.group {
			display: flex;
			align-items: center;
			justify-content: space-between;
			gap: 20rpx;
			margin-top: 20rpx;
			.reset,
			.result {
				display: flex;
				align-items: center;
				justify-content: center;
				font-weight: bold;
				font-size: 40rpx;
				box-shadow: inset 1px 1px 1px 0px rgba(255, 255, 255, 0.8),
										inset -1px -1px 1px 0px rgba(40, 49, 85, 0.3),
										1px 1px 3px 0px rgba(40, 49, 85, 0.1);
				border-radius: 16rpx;
				height: 100rpx;
			}
			.reset {
				width: 40%;
			}
			.result {
				flex: 1;
				background-color: $uni-color-success;
				color: $uni-text-color-inverse;
			}
		}
	}
	.oblique {
		background-color: $uni-bg-color;
		padding: 20rpx;
		.result {
			.title {
				font-weight: bold;
			}
		}
	}
	.wages {
		padding: 30rpx;
		.toggle {
			display: flex;
			align-items: flex-start;
			justify-content: space-between;
			gap: 20rpx;
			.icon {
				margin-top: 30rpx;
			}
			.money {
				flex: 1;
				border: 1rpx solid $uni-border-color;
				border-radius: $uni-border-radius-lg;
				overflow: hidden;
				.name {
					background-color: $uni-bg-color;
					border-bottom: 1rpx solid $uni-border-color;
					padding: 20rpx;
				}
				.num {
					padding: 20rpx;
					background-color: $uni-bg-color;
				}
			}
		}
	}
}
</style>
