@echo off
echo 正在配置Android开发环境...

REM 创建Android SDK目录
if not exist "C:\Android\sdk" (
    mkdir "C:\Android\sdk"
    echo 已创建Android SDK目录: C:\Android\sdk
)

REM 设置环境变量
echo 正在设置环境变量...
setx ANDROID_HOME "C:\Android\sdk" /M
setx JAVA_HOME "C:\Program Files\Java\jdk-23" /M

REM 添加到PATH
for /f "tokens=2*" %%a in ('reg query "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Environment" /v PATH') do set "currentPath=%%b"
echo %currentPath% | find "Android\sdk" >nul
if errorlevel 1 (
    setx PATH "%currentPath%;C:\Android\sdk\platform-tools;C:\Android\sdk\tools;C:\Android\sdk\tools\bin" /M
    echo 已添加Android SDK到PATH
)

echo.
echo 环境变量配置完成！
echo.
echo 接下来需要：
echo 1. 下载Android SDK Command Line Tools
echo 2. 解压到 C:\Android\sdk
echo 3. 重启HBuilderX
echo 4. 在HBuilderX中配置Android SDK路径
echo.
pause
