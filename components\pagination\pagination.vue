<template>
	<view class="pagination">
	  <view class="total">总计：{{ props.total }}件</view>
	  <view class="pagin">
	    <button class="btn btn-primary" @click="handlePrev" :disabled="current === 1">上一页</button>
	    <view class="text">
	      <text class="current">{{ current }}</text>/{{ max }}
	    </view>
	    <button class="btn btn-primary" @click="handleNext" :disabled="current === max">下一页</button>
	  </view>
	</view>
	<view class="mask"></view>
</template>

<script setup>
import { ref, watch } from 'vue'
	
const emits = defineEmits(['change'])
const props = defineProps({
	pageSize: {
		type: Number,
		default: 20
	},
	total: {
		type: Number,
		default: 0
	},
	page: {
		type: Number,
		default: 1
	}
})
watch(
	() => props.total,
	() => {
		init()
	}
)

const current = ref(1)
const max = ref(1)

const handlePrev = () => {
	if (current.value === 1) return
	current.value -= 1
	emits('change', {
		type: 'prev',
		value: current.value
	})
}
const handleNext = () => {
	if (current.value === max) return
	current.value += 1
	emits('change', {
		type: 'next',
		value: current.value
	})
}

const init = () => {
	max.value = Math.ceil(props.total / props.pageSize) || 1
}
</script>

<style lang="scss" scoped>
.pagination {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  background-color: $uni-bg-color;
  box-shadow: 0 6rpx 12rpx 4rpx rgba(0, 0, 0, 0.05),
							0 16rpx 10rpx 2rpx rgba(0, 0, 0, 0.06),
							0 10rpx 10rpx -6rpx rgba(0, 0, 0, 0.05);
  .total {
    display: flex;
    align-items: center;
    justify-content: center;
    color: $uni-text-color-grey;
    height: 40rpx;
  }
  .pagin {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20rpx;
    height: 80rpx;
    .text {
      font-size: 32rpx;
      .current {
				color: $uni-color-primary;
      }
    }
  }
}
.mask {
  height: 120rpx;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
</style>