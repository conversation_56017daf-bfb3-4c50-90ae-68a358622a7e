//
//  TCBeautyPanel.h
//  TCBeautyPanel
//
//  Created by cui on 2019/12/20.
//  Copyright © 2019 Tencent. All rights reserved.
//

#import <Foundation/Foundation.h>

//! Project version number for TCBeautyPanel.
FOUNDATION_EXPORT double TCBeautyPanelVersionNumber;

//! Project version string for TCBeautyPanel.
FOUNDATION_EXPORT const unsigned char TCBeautyPanelVersionString[];

// In this header, you should import all the public headers of your framework using statements like #import <TCBeautyPanel/PublicHeader.h>

#import <TCBeautyPanel/TCBeautyPanelView.h>
#import <TCBeautyPanel/TCBeautyPanelActionPerformer.h>
#import <TCBeautyPanel/TCFilter.h>
#import <TCBeautyPanel/TCBeautyPanelTheme.h>
#import <TCBeautyPanel/TCMenuView.h>
#import <TCBeautyPanel/TCBeautyPanelActionProxy.h>
