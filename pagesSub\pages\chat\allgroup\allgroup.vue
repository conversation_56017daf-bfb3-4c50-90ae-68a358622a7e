<template>
	<view class="bgColor">
		<view class="search">
			<view class="item">
				<!-- <view class="label">群名称</view> -->
				<input class="input" v-model="styleName" type="text" placeholder="请输入关键字">
				<button class="btn" @click="resetEvent">重置</button>
				<button class="btn submit" @click="searchEvent">搜索</button>
				<button class="btn submit" @click="createEvent">创建群</button>
			</view>
		</view>
		<view class="wxgrouplist">
			<view class="wxgrouplist-item" v-for="(v,i) in list" :key="i" @click="goGroup(v)">
				<!-- <view class="wxgrouplist-imgs">
					<image v-for="(item,index) in v.portrait" :src="item" mode="aspectFill"></image>
				</view> -->
				<view class="wxgrouplist-imgs">
					<image src="../../../static/index/other/im_1.jpg" mode="aspectFill"></image>
				</view>
				<view class="wxgrouplist-title">{{v.groupName}}</view>
				<view class="wxgrouplist-btn">
					<view class="btn">进群</view>
				</view>
			</view>
		</view>
	</view>
	<popup :show="isTask"  @close="handleTask">
		<scroll-view scroll="auto" enable-flex="true" scroll-y="true">
			<view class="detail">
				<view class="title">
					<text>新建群聊</text>
				</view>
				<view class="cont" height="60vh">
					<view class="jobs" height="20vh">
						<view class="item">
							<!-- <view class="label">群名称</view> -->
							<input class="input" maxlength="20" name="name" type="text" v-model="groupName"
								placeholder="请输入群名称"
							>
						</view>
						<view class="item">
							<view class="type_chose">
								<text class="text">请选择群标签(多选)</text>
								<uni-data-checkbox mode="tag" :multiple="true" v-model="radioJBGYBZ" :localdata="JBGYBZ"></uni-data-checkbox>
							</view>
						</view>
						<view class="item btnView">
							<button class="btn submit" @click="saveEvent">提 交</button>
						</view>
					</view>
				</view>
			</view>
		</scroll-view>
	</popup>
</template>

<script setup>
	import {
		ref,
		toRaw
	} from 'vue'
	import {
		onLoad,
		onShow,
		onNavigationBarButtonTap
	} from "@dcloudio/uni-app"
	import Https from '@/apis/index.js'
	import { linkTimer, isEmpty, isPhone } from '@/utils/index.js'
	import StoreIm from '@/store/index.js';
	import HttpIm from '@/common/request.js'
	let styleName = ref("")
	let groupName = ref("")
	const list = ref([])
	let radioJBGYBZ = ref([])
	const JBGYBZ= [{
		text: '服装',
		value: '服装'
		}, {
			text: '箱包',
			value: '箱包'
		}, {
			text: '鞋',
			value:  '鞋'
		}, {
			text: '家具',
			value:'家具'
		}, {
			text: '家用工具',
			value:'家用工具'
		}];
	onShow(() => {
		searchEvent();
	})
	const isTask = ref(false)
	const handleTask = () => {
		isTask.value = !isTask.value
		groupName.value=""
		radioJBGYBZ.value=[]
	}
	const goGroup=(e)=>{
		let obj={
			userId:e.groupId,
			windowType:"GROUP",
			portrait:JSON.stringify(e.portrait)
		}
		 console.log(obj)
		uni.navigateTo({
			url: '../chatWindow/index?data=' + encodeURIComponent(JSON.stringify(obj))
		})
	}
	const getlist=(e)=>{
		this.$http.request({
			url: '/group/groupList',
			success: res => {
				if (res.data.code == 200) {
					this.list.values = res.data.data;
					console.log(this.list)
				}
			}
		});
	}
	const resetEvent=()=>{
		styleName.value=""
		getGroup();
	}
	const searchEvent=()=>{
		getGroup();
	}
	const createEvent=()=>{
		// groupName.value=""
		isTask.value = true;
	}
	const saveEvent=()=>{
		if (isEmpty(groupName.value)) {
			uni.showToast({
				title: '请输入群名称',
				icon: 'error'
			})
			return
		}
		//加入群聊
		StoreIm.dispatch('get_UserInfo').then(res=>{
			let currUser = res;
			var ids = [];
			console.log(currUser)
			ids.push(currUser.userId);
			console.log(ids)
			
			// HttpIm.request({
			// 	url: '/group/createGroup',
			// 	method: 'POST',
			// 	data:JSON.stringify(ids),
			// 	success: (res) => {
			// 	}
			// });
			
			let r=  Https.Chat.CreateGroup({
				groupName:groupName.value,
				label:toRaw(radioJBGYBZ.value).join(','),
				userId: ids[0]
			}).then(res=>{
				console.log(res)
				console.log(res.ErrCode)
				if(res.ErrCode=="0"){
					isTask.value = false;
					groupName.value="";
					searchEvent();
				}
			});
		});
	}
	const getGroup = async() => {
		let r= await Https.Chat.GroupList({
			groupId:'',
			groupName:styleName.value
		})
		console.log(r)
		list.value.splice(0)
		if(r.ResData.length>0){
			r.ResData.forEach(function(item,index){
				list.value.push(item)
			})
		}
		
		console.log(list.value)
	}
</script>

<style lang="scss" scoped>
/* #ifdef APP-PLUS */
.bgColor {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: #ededed;
	overflow: auto;
}
/* #endif */
/* #ifdef H5 */
page {
	background: #ededed;
}
/* #endif */
.wxgrouplist{
	display: flex;flex-direction: column;
}
.wxgrouplist-item{
	padding:0 28rpx;
	background-color: #fff;
	display: flex;flex-direction: row;align-items: center;
}
.wxgrouplist-imgs{
	width: 80rpx;
	height: 80rpx;
	border-radius: 12rpx;
	overflow: hidden;
	display: flex;flex-direction: row;flex-wrap: wrap;
	background-color: #DEDEDE;
}
.wxgrouplist-imgs image{
	/* margin: 2rpx;
	width: 36rpx;
	height: 36rpx; */
}

.wxgrouplist-imgs image{
	margin: 2rpx;
	width: 78rpx;
	height:78rpx;
}

.wxgrouplist-title{
	flex: 1;
	border-bottom: 1px #F4F4F4 solid;
	font-size: 32rpx;
	color: #333;
	line-height: 105rpx;
	margin-left: 38rpx;
}
.wxgrouplist-btn{
	/* flex: 1; */
	border-bottom: 1px #F4F4F4 solid;
	/* font-size: 32rpx; */
	line-height: 105rpx;
	/* margin-left: 38rpx; */
}
.wxgrouplist-btn .btn{
	font-size: 32rpx;
	margin: 16rpx;
	color: red;
	width: 150rpx;
	height: 55rpx;
	border-radius: 12rpx;
	border: 1px #DEDEDE solid;
}
.search {
	display: grid;
	grid-template-columns: repeat(1, minmax(0, 1fr));
	padding: 20rpx;
	gap: 20rpx;
	background-color: $uni-bg-color;
	// border-bottom: 1px solid gray !important;
	.item {
		display: flex;
		align-items: center;
		gap: 20rpx;
		font-size: 28rpx;
		.label {
			flex-shrink: 0;
			width: 120rpx;
		}
		.input {
			flex: 1;
			background-color: $uni-bg-color-grey;
			border-radius: 10rpx;
			padding: 0 20rpx;
			min-height: 60rpx;
			width: 200rpx;
			.value {
				display: flex;
				align-items: center;
				justify-content: space-between;
				min-height: 60rpx;
				&::after {
					font-family: 'iconfont';
					content: '\e840';
					color: $uni-text-color-grey;
				}
				&:empty::before {
					content: '请选择';
					color: gray;
				}
			}
		}
	}
	.submit {
		background-color: $uni-color-primary;
		color: $uni-text-color-inverse;
	}
	.btnGroup{
		 width: 100rpx !important;
	}
}
	
.detail {
	max-height:60vh;
	padding: 0rpx 20rpx 20rpx 20rpx;
	background-color: $uni-bg-color;
	.item{
		display: flex;
		align-items: center;
		justify-content: space-between;
		gap: 0.625rem;
		min-height: 4rem;
		padding: 0 0.625rem;
		&:nth-child(2) {
			// width: 80rpx;
			display: block;
			margin-top: 20rpx;
		}
		&:nth-child(3) {
			margin-top: 50rpx;
		}
		.input{
			border: 1px gray solid;
			height: 76rpx;
			width: 100%;
			border-radius: 10rpx;
		}
		.submit{
			width:100%;
			height: 76rpx;
			color: white;
			background-color: #007aff;
		}
	}
	
	.title {
		display: flex;
		align-items: center;
		justify-content: space-between;
		height: 80rpx;
		padding: 0 0rpx;
		border-bottom: 1rpx solid $uni-border-color;
		font-weight: bold;
		.status {
			font-size: 26rpx;
			font-weight: normal;
			&.success {
				color: $uni-color-primary;
			}
			&.fail {
				color: $uni-color-error;
			}
		}
	}
	.cont {
		.jobs {
			font-size: 28rpx;
			.scrollView{
				max-height: 79vh;
				padding: 20rpx 20rpx 20rpx 20rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;
			}
			.row {
				padding: 20rpx 20rpx 20rpx 20rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;
				&:nth-child(odd) {
					background-color: #f5f9ff;
				}
				.cell {
					&:nth-child(1) {
						width: 180rpx;
					}
					&:nth-child(2) {
						flex: 1;
					}
					.item {
						display: flex;
						align-items: center;
						justify-content: space-between;
						.child {
							padding: 10rpx 6rpx;
							display: flex;
							align-items: center;
								justify-content: center;
							&:nth-child(1),
							&:nth-child(2) {
								width: 100rpx;
							}
							&:nth-child(3) {
								flex: 1;
							}
							.name {
								flex: 1;
								.primary {
									color: $uni-color-primary;
								}
							}
						}
					}
				}
			}
		}
	}
	
}
scroll-view::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
  border-radius: 0;
  background-color: transparent;
  color: transparent;
}

.type_chose {
		position: fixed;
		left: 60rpx;
		right: 60rpx;
		color: #EF3675;
		height: 60rpx;
		line-height: 60rpx;
		font-size: 24px;
		font-weight: bold;
		.text{
			color:rgb(102, 102, 102);
			font-size: 17px;
		}
	}

.btnView{
	margin-top: 200rpx;
}
</style>
