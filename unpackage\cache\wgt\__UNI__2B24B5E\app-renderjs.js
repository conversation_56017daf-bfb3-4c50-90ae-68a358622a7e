var __renderjsModules={};
__renderjsModules["5a1e922e"]=(()=>{var u=Object.defineProperty;var c=Object.getOwnPropertyDescriptor;var f=Object.getOwnPropertyNames;var p=Object.prototype.hasOwnProperty;var m=(t,e)=>{for(var o in e)u(t,o,{get:e[o],enumerable:!0})},g=(t,e,o,a)=>{if(e&&typeof e=="object"||typeof e=="function")for(let i of f(e))!p.call(t,i)&&i!==o&&u(t,i,{get:()=>e[i],enumerable:!(a=c(e,i))||a.enumerable});return t};var v=t=>g(u({},"__esModule",{value:!0}),t);var S={};m(S,{default:()=>D});var l={showWatch(t,e,o,a,i){var s=i.state,r=o.$el||o.$vm&&o.$vm.$el;if(r){if(this.getDom(a,o,i),t&&t!=="none"){this.openState(t,a,o,i);return}s.left&&this.openState("none",a,o,i),this.resetTouchStatus(a,i)}},touchstart(t,e,o){let a=t.instance,i=a.getDataset().disabled,s=o.state;this.getDom(a,e,o),i=this.getDisabledType(i),!i&&(a.requestAnimationFrame(function(){a.removeClass("ani"),e.callMethod("closeSwipe")}),s.x=s.left||0,this.stopTouchStart(t,e,o))},touchmove(t,e,o){let a=t.instance;if(!a)return;let i=a.getDataset().disabled,s=o.state;if(i=this.getDisabledType(i),i||(this.stopTouchMove(t,o),s.direction!=="horizontal"))return;t.preventDefault&&t.preventDefault();let r=s.x+s.deltaX;this.move(r,a,e,o)},touchend(t,e,o){let a=t.instance,i=a.getDataset().disabled,s=o.state;i=this.getDisabledType(i),!i&&this.moveDirection(s.left,a,e,o)},move(t,e,o,a){t=t||0;let i=a.state,s=i.leftWidth,r=i.rightWidth;i.left=this.range(t,-r,s),e.requestAnimationFrame(function(){e.setStyle({transform:"translateX("+i.left+"px)","-webkit-transform":"translateX("+i.left+"px)"})})},getDom(t,e,o){var a=o.state,i=e.$el||e.$vm&&e.$vm.$el,s=i.querySelector(".button-group--left"),r=i.querySelector(".button-group--right");a.leftWidth=s.offsetWidth||0,a.rightWidth=r.offsetWidth||0,a.threshold=t.getDataset().threshold},getDisabledType(t){return(typeof t=="string"?JSON.parse(t):t)||!1},range(t,e,o){return Math.min(Math.max(t,e),o)},moveDirection(t,e,o,a){var i=a.state,s=i.threshold,r=i.position,h=i.isopen||"none",n=i.leftWidth,d=i.rightWidth;if(i.deltaX===0){this.openState("none",e,o,a);return}h==="none"&&d>0&&-t>s||h!=="none"&&d>0&&d+t<s?this.openState("right",e,o,a):h==="none"&&n>0&&t>s||h!=="none"&&n>0&&n-t<s?this.openState("left",e,o,a):this.openState("none",e,o,a)},openState(t,e,o,a){let i=a.state,s=i.leftWidth,r=i.rightWidth,h="";switch(i.isopen=i.isopen?i.isopen:"none",t){case"left":h=s;break;case"right":h=-r;break;default:h=0}i.isopen!==t&&(i.throttle=!0,o.callMethod("change",{open:t})),i.isopen=t,e.requestAnimationFrame(()=>{e.addClass("ani"),this.move(h,e,o,a)})},getDirection(t,e){return t>e&&t>10?"horizontal":e>t&&e>10?"vertical":""},resetTouchStatus(t,e){let o=e.state;o.direction="",o.deltaX=0,o.deltaY=0,o.offsetX=0,o.offsetY=0},stopTouchStart(t,e,o){let a=t.instance,i=o.state;this.resetTouchStatus(a,o);var s=t.touches[0];i.startX=s.clientX,i.startY=s.clientY},stopTouchMove(t,e){let o=t.instance,a=e.state,i=t.touches[0];a.deltaX=i.clientX-a.startX,a.deltaY=i.clientY-a.startY,a.offsetY=Math.abs(a.deltaY),a.offsetX=Math.abs(a.deltaX),a.direction=a.direction||this.getDirection(a.offsetX,a.offsetY)}};var D={mounted(t,e,o){this.state={}},methods:{showWatch(t,e,o,a){l.showWatch(t,e,o,a,this)},touchstart(t,e){l.touchstart(t,e,this)},touchmove(t,e){l.touchmove(t,e,this)},touchend(t,e){l.touchend(t,e,this)}}};return v(S);})();

__renderjsModules["2651fd3c"]=(()=>{var m=Object.defineProperty,E=Object.defineProperties,O=Object.getOwnPropertyDescriptor,R=Object.getOwnPropertyDescriptors,$=Object.getOwnPropertyNames,b=Object.getOwnPropertySymbols;var g=Object.prototype.hasOwnProperty,B=Object.prototype.propertyIsEnumerable;var w=(t,e,o)=>e in t?m(t,e,{enumerable:!0,configurable:!0,writable:!0,value:o}):t[e]=o,v=(t,e)=>{for(var o in e||(e={}))g.call(e,o)&&w(t,o,e[o]);if(b)for(var o of b(e))B.call(e,o)&&w(t,o,e[o]);return t},x=(t,e)=>E(t,R(e));var S=(t,e)=>{for(var o in e)m(t,o,{get:e[o],enumerable:!0})},A=(t,e,o,a)=>{if(e&&typeof e=="object"||typeof e=="function")for(let i of $(e))!g.call(t,i)&&i!==o&&m(t,i,{get:()=>e[i],enumerable:!(a=O(e,i))||a.enumerable});return t};var D=t=>A(m({},"__esModule",{value:!0}),t);var P=(t,e,o)=>new Promise((a,i)=>{var r=n=>{try{p(o.next(n))}catch(s){i(s)}},l=n=>{try{p(o.throw(n))}catch(s){i(s)}},p=n=>n.done?a(n.value):Promise.resolve(n.value).then(r,l);p((o=o.apply(t,e)).next())});var I={};S(I,{default:()=>z});var f=t=>t.then(e=>[null,e]).catch(e=>[e,{}]);var T=t=>{if(t)return new Promise((e,o)=>{let a=new FileReader;a.onloadend=()=>{let i=a.result;e(i)},a.onerror=()=>{o({mode:"fileToBase64",data:{errMsg:"File to base64 fail."}})},a.readAsDataURL(t)})};var M=(t,e={},o)=>{let{url:a,header:i,formData:r}=t;return new Promise((l,p)=>{let n=new XMLHttpRequest;n.open("POST",a,!0);for(let s in i)n.setRequestHeader(s,i[s]);o&&(n.upload.onprogress=o),n.onreadystatechange=function(){n.readyState===4&&(n.status===200?l(x(v({},e),{response:JSON.parse(n.responseText)})):p({mode:"uploadFile",data:{data:n.responseText,errMsg:"uploadFile fail."}}))},n.send(r)})};var z={data(){return{id:0,uploadOptions:{}}},methods:{renderProps(t){let{id:e,renderInput:o,upload:a}=t;o&&(this.id=e,this.uploadOptions=a,this.$nextTick(()=>{var r;let i=document.getElementById(`xe-upload-${e}`);i.addEventListener("change",()=>{this.handleUpload()}),(r=i==null?void 0:i.click)==null||r.call(i)}))},handleUpload(){return P(this,null,function*(){let{url:t,name:e,header:o={},formData:a={}}=this.uploadOptions||{},i=document.getElementById(`xe-upload-${this.id}`);if(!i.files[0])return;let r=Array.from(i.files),l=[];for(let s=0;s<r.length;s+=1){let d=r[s],u="file";d.type.includes("image")&&(u="image"),d.type.includes("video")&&(u="video");let h={size:d.size,name:d.name,type:d.type,fileType:u,tempFilePath:"",base64Url:""};if(!t){let[c,F]=yield f(T(i.files[s]));c||(h.base64Url=F),l.push(h);continue}let y=new FormData;y.append(e,i.files[s],d.name);for(let c in a)y.append(c,a[c]);let U=c=>{if(c.lengthComputable){var F=c.loaded/c.total*100;this.handleRenderEmits({type:"onprogress",data:{progress:Math.floor(F),current:s+1,total:r.length}})}};l.push(M({url:t,header:o,formData:y},h,U))}if(!t)return this.handleRenderEmits({type:"choose",data:l});this.handleRenderEmits({type:"onprogress",data:{progress:0,current:1,total:r.length}});let[p,n]=yield f(Promise.all(l));if(p)return this.handleRenderEmits({type:"warning",data:p});this.handleRenderEmits({type:"success",data:n})})},handleRenderEmits(t){this.$ownerInstance.callMethod("handleEmits",t)}}};return D(I);})();
