import { _ as _export_sfc, u as userInfoStore, o as onShow, a as onLoad, f as formatAppLog, b as onUnload, c as onNavigationBarButtonTap, d as apis, r as resolveEasycom, e as __easycom_0 } from "../../../index.js";
import { ref, toRaw, onMounted, reactive, resolveDynamicComponent, resolveComponent, openBlock, createElementBlock, createElementVNode, createCommentVNode, normalizeStyle, normalizeClass, createVNode, withCtx, toDisplayString, createTextVNode } from "vue";
import { storeToRefs } from "pinia";
const _style_0 = { "tabsviewContent": { "": { "position": "fixed", "!minWidth": "750rpx" } }, "tabsview": { "": { "!minWidth": "750rpx" } }, "imgV": { "": { "width": 100, "textAlign": "center", "minHeight": 65 } }, "search": { "": { "gridTemplateColumns": "repeat(1, minmax(0, 1fr))", "paddingTop": "20rpx", "paddingRight": "20rpx", "paddingBottom": "20rpx", "paddingLeft": "20rpx", "gap": "20rpx", "backgroundColor": "#ffffff" }, ".top_nav .top_content ": { "position": "absolute", "right": 20, "top": "35rpx" } }, "search_item": { "": { "display": "flex", "flexDirection": "row", "alignItems": "center", "gap": "20rpx", "fontSize": "28rpx" } }, "search_item_btn_submit": { "": { "backgroundColor": "#007aff", "color": "#ffffff" } }, "submit_deploy": { "": { "position": "fixed", "left": "20rpx", "right": "20rpx", "backgroundColor": "#007aff", "color": "#ffffff", "height": "80rpx", "bottom": "10rpx" } }, "person-head": { "": { "position": "relative", "backgroundColor": "#ffffff", "marginLeft": "20rpx", "marginRight": "20rpx" } }, "video-layer": { "": { "position": "absolute", "right": 12, "bottom": 120, "color": "#ffffff" } }, "uniui-right": { "": { "justifyContent": "center" } }, "videoTitle": { "": { "paddingTop": 5, "paddingRight": 5, "paddingBottom": 5, "paddingLeft": 5, "color": "#de4a00", "fontSize": 13, "lines": 13, "whiteSpace": "normal" } }, "userAvatar": { "": { "width": 35, "height": 35, "borderRadius": 100, "marginBottom": 10, "borderWidth": "1rpx", "borderStyle": "solid", "borderColor": "#ffffff", "backgroundColor": "#fafafa" } }, "iconTitle": { "": { "fontSize": 12, "color": "#ffffff", "textAlign": "center", "paddingBottom": 5 } }, "top_nav": { "": { "position": "fixed", "top": 0, "left": 0, "right": 0, "backgroundImage": "linear-gradient(to bottom, rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0))" } }, "top_content": { ".top_nav ": { "paddingTop": "30rpx", "flexDirection": "row", "alignItems": "center", "justifyContent": "center" } }, "player": { ".top_nav .top_content ": { "position": "absolute", "left": 20, "top": "35rpx" } }, "img": { ".top_nav .top_content ": { "width": "44rpx", "height": "44rpx" } }, "content_btn": { ".top_nav .top_content ": { "flexDirection": "row", "width": 220, "alignItems": "center", "justifyContent": "space-around" } }, "content_item": { ".top_nav .top_content .content_btn ": { "position": "relative", "height": 30 } }, "line_on": { ".top_nav .top_content .content_btn .content_item ": { "position": "absolute", "width": 30, "height": 2, "backgroundColor": "#FFFFFF", "bottom": 0, "left": 2, "borderRadius": "4rpx" } }, "item_title": { ".top_nav .top_content .content_btn .content_item ": { "color": "#dcdcdc", "fontSize": "36rpx", "fontWeight": "bold" } }, "i_on": { ".top_nav .top_content .content_btn .content_item ": { "fontWeight": "bold", "fontSize": "38rpx", "!color": "#FFFFFF" } }, "uContent": { "": { "height": "220rpx", "lineHeight": "220rpx", "borderRadius": "20rpx" } } };
const bgColor = "#fff";
const lineColor = "#2979ff";
const effect3d = true;
const effect3dMargin = 40;
const autoplay = false;
const vertical = false;
const fullScreen = true;
const topFloat = true;
const fotterFloat = true;
const mode = "round";
const indicatorPos = "bottomCenter";
const dotIndex = 0;
const dotFloatIndex = 0;
const _sfc_main = {
  __name: "index",
  setup(__props, { expose: __expose }) {
    __expose();
    const store = userInfoStore();
    const {
      user,
      roleID,
      teamID
    } = storeToRefs(store);
    let current = ref(0);
    const tabs = ["发货", "招工", "接货"];
    let listFH = ref([]);
    let listZG = ref([]);
    let listSH = ref([]);
    let currVideoId = "";
    let activeIndex = 1;
    const ad = ref([]);
    let styleName = ref("");
    const groupList = ref([]);
    let phone = ref("");
    const win = uni.getSystemInfoSync();
    const width = win.windowWidth;
    const height = win.windowHeight;
    const bottomStyle = {
      // "position": "absolute",
      "top": "36px"
      // "left": "0",
      // "display": "flex",
      // "flex-wrap": "wrap",
      // "flex-direction": "column"
    };
    const realList = ref([]);
    const currentVedio = ref(0);
    let context = null;
    const counter = ref(0);
    let windowWidth = 0;
    let statusBarHeight = 0;
    const onchange = (index2, size) => {
      formatAppLog("log", "at pagesSub/pages/adv/index.nvue:244", "onchange-当前索引:", index2 + "aa" + size);
      currentVedio.value = index2;
      if (index2 == 0) {
        uni.showToast({ title: "当前已是第一个视频", icon: "none", mask: false });
      }
      if (index2 == size - 1) {
        uni.showToast({ title: "当前已是最后一个视频", icon: "none", mask: false });
      }
    };
    const loadMore = (index2, size) => {
      formatAppLog("log", "at pagesSub/pages/adv/index.nvue:256", "加载更所视频：", index2 + " / " + size);
      if (counter.value > 5)
        return;
      getList().forEach((item) => {
        item.title = realList.value.length + "，" + item.title + item.title + item.title;
        realList.value.push(item);
      });
      counter.value = counter.value + 1;
    };
    const play = (context2) => {
      formatAppLog("log", "at pagesSub/pages/adv/index.nvue:270", "视频开始播放");
    };
    const pause = (context2) => {
      formatAppLog("log", "at pagesSub/pages/adv/index.nvue:277", "视频暂停播放");
    };
    const ended = (context2) => {
      formatAppLog("log", "at pagesSub/pages/adv/index.nvue:284", "视频播放结束");
    };
    const error = (context2, event) => {
      formatAppLog("error", "at pagesSub/pages/adv/index.nvue:291", "视频播放出错：", event);
    };
    const waiting = (context2) => {
      formatAppLog("error", "at pagesSub/pages/adv/index.nvue:298", "视频出现缓冲");
    };
    const videoClick = (context2, video) => {
      formatAppLog("log", "at pagesSub/pages/adv/index.nvue:305", "点击了视频：", video);
    };
    const doubleClick = (context2, video) => {
      phone.value = video.author.tel;
      formatAppLog("log", "at pagesSub/pages/adv/index.nvue:313", "双击了视频：", video);
      uni.showModal({
        title: "提示",
        content: "拨打电话给发布人？",
        confirmText: "拨打",
        cancelText: "取消",
        success: function(res) {
          if (res.confirm) {
            uni.makePhoneCall({
              phoneNumber: phone.value
            });
          } else if (res.cancel) {
            formatAppLog("log", "at pagesSub/pages/adv/index.nvue:341", "用户点击取消");
          }
        }
      });
    };
    const maskClick = (index2, video) => {
      context = context;
      formatAppLog("log", "at pagesSub/pages/adv/index.nvue:350", "点击了蒙层：", index2, video);
    };
    const getList = () => {
      return [{
        videoId: realList.value.length + 1,
        title: "。",
        poster: "https://i02piccdn.sogoucdn.com/2acf176d90718d73",
        url: "https://txmov2.a.yximgs.com/upic/2020/11/08/19/BMjAyMDExMDgxOTQxNTlfNTIzNDczMzQ0XzM4OTQ1MDk5MTI4XzFfMw==_b_Bc770a92f0cf153407d60a2eddffeae2a.mp4",
        uploadTime: "2023-11-08 19:41",
        ipLocation: "上海",
        author: {
          authorId: 101,
          avatar: "https://i02piccdn.sogoucdn.com/2acf176d90718d73",
          nickName: "陌路",
          genderName: "男"
        }
      }];
    };
    onShow(() => {
      let userInfo = toRaw(user.value);
      if (userInfo.ID == "") {
        store.clear();
        uni.reLaunch({
          url: "/pagesSub/pages/login/login"
        });
      }
      if (roleID.value == "") {
        uni.reLaunch({
          url: "/pages/my/my"
        });
      }
      getList().forEach((item) => {
        item.title = realList.value.length + "，" + item.title + item.title + item.title;
        realList.value.push(item);
      });
    });
    onLoad((e) => {
      formatAppLog("log", "at pagesSub/pages/adv/index.nvue:399", "onload");
      formatAppLog("log", "at pagesSub/pages/adv/index.nvue:400", e);
      this.statusBarHeight = uni.getSystemInfoSync().statusBarHeight + "px";
      this.windowWidth = uni.getSystemInfoSync().screenWidth;
      getAdv();
    });
    onUnload(() => {
      const videoContext = uni.createVideoContext("myVideo", this);
      videoContext.pause();
      this.mp4Url = "";
    });
    onNavigationBarButtonTap(() => {
      uni.navigateTo({
        url: "/pagesSub/pages/message/message"
      });
    });
    onMounted(() => {
      formatAppLog("log", "at pagesSub/pages/adv/index.nvue:419", "onMounted");
      getAdv();
    });
    const currPlay = (e) => {
      currVideoId = e;
    };
    const changeEvent = (e) => {
      current.value = e;
      formatAppLog("log", "at pagesSub/pages/adv/index.nvue:428", e);
      formatAppLog("log", "at pagesSub/pages/adv/index.nvue:429", current.value);
      formatAppLog("log", "at pagesSub/pages/adv/index.nvue:430", currVideoId);
    };
    const handleAdClick = (position) => {
      formatAppLog("log", "at pagesSub/pages/adv/index.nvue:439", "handleAdClick " + position);
    };
    const changeTab = (index2) => {
      formatAppLog("log", "at pagesSub/pages/adv/index.nvue:445", index2);
    };
    const resetEvent = () => {
      styleName.value = "";
      getGroup();
    };
    const searchEvent = () => {
      getGroup();
    };
    const publishEvent = () => {
      uni.removeStorageSync("storage_orderinfo");
      if (current.value == 0) {
        uni.navigateTo({
          url: "/pagesSub/pages/adv/publishFH"
        });
      }
      if (current.value == 1) {
        uni.navigateTo({
          url: "/pagesSub/pages/adv/publishZG"
        });
      }
      if (current.value == 2) {
        uni.navigateTo({
          url: "/pagesSub/pages/adv/publishSH"
        });
      }
    };
    const getGroup = async () => {
      let r = await apis.Chat.GroupList({
        groupId: "",
        groupName: styleName.value
      });
      formatAppLog("log", "at pagesSub/pages/adv/index.nvue:481", r);
      groupList.value.splice(0);
      if (r.ResData.length > 0) {
        r.ResData.forEach(function(item, index2) {
          groupList.value.push(item);
        });
      }
    };
    const getAdv = async () => {
      listFH.value.splice(0);
      listSH.value.splice(0);
      listZG.value.splice(0);
      var groupId = uni.getStorageSync("storage_orderinfo_groupid");
      formatAppLog("log", "at pagesSub/pages/adv/index.nvue:496", groupId);
      apis.Adv.SmallList({
        groupid: groupId
      }).then((function(res) {
        formatAppLog("log", "at pagesSub/pages/adv/index.nvue:500", res);
        if (res.Success && res.ResData.length > 0) {
          res.ResData.forEach(function(item, index2) {
            if (item.type == "SH") {
              listSH.value.push({
                videoId: listSH.value.length + 1,
                title: "接货消息:\n	地址:" + item.sh_address + "\n	姓名:" + item.sh_name + "\n	联系电话:" + item.sh_tel + "\n	人数规模:" + item.sh_gm + "人 \n	擅长款式:" + item.sh_style + "\n	包料能力:" + item.sh_blnl + "\n	打版开发能力:" + item.sh_dbkfnl + "\n	加工难度:" + item.sh_jgnd + "\n	",
                poster: "https://img1.baidu.com/it/u=1314619043,374926406&fm=253&fmt=auto?w=348&h=500",
                //'https://i02piccdn.sogoucdn.com/2acf176d90718d73",
                url: apis.Url.baseUrlBiz + "/" + item.sh_mat_path,
                uploadTime: "2024-10-02 09:41",
                ipLocation: "北京",
                author: {
                  authorId: 102,
                  avatar: "https://i02piccdn.sogoucdn.com/2acf176d90718d73",
                  nickName: "管理员",
                  genderName: "女",
                  tel: item.sh_tel
                }
              });
            }
            if (item.type == "FH") {
              listFH.value.push({
                videoId: listFH.value.length + 1,
                title: "发货消息:\n	联系电话:" + item.fh_Tel + "\n	期望加工地址:" + item.fh_address + "\n	工期限制:" + item.fh_limitGQ + "\n	基本服装类型:" + item.fh_fzType + "\n	基本工艺标准:" + item.fh_JBGYBZ + "\n	账期期望:" + item.fh_ZQQW + "\n	订单数量:" + item.fh_orderNum + "\n	是否包裁:" + item.fh_SFBC + "\n	是否包面辅料:" + item.fh_SFMLFZ + "\n	",
                poster: "https://img1.baidu.com/it/u=1314619043,374926406&fm=253&fmt=auto?w=348&h=500",
                //'https://i02piccdn.sogoucdn.com/2acf176d90718d73",
                url: apis.Url.baseUrlBiz + "/" + item.fh_mat_path,
                uploadTime: "2024-10-02 09:41",
                ipLocation: "北京",
                author: {
                  authorId: 102,
                  avatar: "https://i02piccdn.sogoucdn.com/2acf176d90718d73",
                  nickName: "管理员",
                  genderName: "女",
                  tel: item.fh_Tel
                }
              });
            }
            if (item.type == "ZG") {
              var zhaopinList = JSON.parse(item.zg_zaopinList);
              var gwList = "";
              zhaopinList.forEach(function(item1, index3) {
                gwList += item1.type + "  招聘人数:" + item1.quantity + "人\n	";
              });
              listZG.value.push({
                videoId: listZG.value.length + 1,
                title: "招聘通知:\n	地址:" + item.zg_address + "\n	姓名:" + item.zg_name + "\n	联系电话:" + item.zg_tel + "\n	招聘信息:\n	" + gwList,
                poster: "https://img1.baidu.com/it/u=1314619043,374926406&fm=253&fmt=auto?w=348&h=500",
                //'https://i02piccdn.sogoucdn.com/2acf176d90718d73",
                url: apis.Url.baseUrlBiz + "/" + item.zg_mat_path,
                uploadTime: "2024-10-02 09:41",
                ipLocation: "北京",
                author: {
                  authorId: 102,
                  avatar: "https://i02piccdn.sogoucdn.com/2acf176d90718d73",
                  nickName: "管理员",
                  genderName: "女",
                  tel: item.zg_tel
                }
              });
            }
          });
        }
      }).bind(this));
      apis.Adv.BigList({
        groupid: groupId
      }).then((function(res) {
        formatAppLog("log", "at pagesSub/pages/adv/index.nvue:587", res);
        if (res.Success && res.ResData.length > 0) {
          res.ResData.forEach(function(item, index2) {
            if (item.type == "SH") {
              listSH.value.push({
                videoId: listSH.value.length + 1,
                title: "接货消息:\n	地址:" + item.sh_address + "\n	姓名:" + item.sh_name + "\n	联系电话:" + item.sh_tel + "\n	人数规模:" + item.sh_gm + "人 \n	擅长款式:" + item.sh_style + "\n	包料能力:" + item.sh_blnl + "\n	打版开发能力:" + item.sh_dbkfnl + "\n	加工难度:" + item.sh_jgnd + "\n	",
                poster: "https://img1.baidu.com/it/u=1314619043,374926406&fm=253&fmt=auto?w=348&h=500",
                //'https://i02piccdn.sogoucdn.com/2acf176d90718d73",
                url: apis.Url.baseUrlBiz + "/" + item.sh_mat_path,
                uploadTime: "2024-10-02 09:41",
                ipLocation: "北京",
                author: {
                  authorId: 102,
                  avatar: "https://i02piccdn.sogoucdn.com/2acf176d90718d73",
                  nickName: "管理员",
                  genderName: "女",
                  tel: item.sh_tel
                }
              });
            }
            if (item.type == "FH") {
              listFH.value.push({
                videoId: listFH.value.length + 1,
                title: "发货消息:\n	联系电话:" + item.fh_Tel + "\n	期望加工地址:" + item.fh_address + "\n	工期限制:" + item.fh_limitGQ + "\n	基本服装类型:" + item.fh_fzType + "\n	基本工艺标准:" + item.fh_JBGYBZ + "\n	账期期望:" + item.fh_ZQQW + "\n	订单数量:" + item.fh_orderNum + "\n	是否包裁:" + item.fh_SFBC + "\n	是否包面辅料:" + item.fh_SFMLFZ + "\n	",
                poster: "https://img1.baidu.com/it/u=1314619043,374926406&fm=253&fmt=auto?w=348&h=500",
                //'https://i02piccdn.sogoucdn.com/2acf176d90718d73",
                url: apis.Url.baseUrlBiz + "/" + item.fh_mat_path,
                uploadTime: "2024-10-02 09:41",
                ipLocation: "北京",
                author: {
                  authorId: 102,
                  avatar: "https://i02piccdn.sogoucdn.com/2acf176d90718d73",
                  nickName: "管理员",
                  genderName: "女",
                  tel: item.fh_Tel
                }
              });
            }
            if (item.type == "ZG") {
              var zhaopinList = JSON.parse(item.zg_zaopinList);
              var gwList = "";
              zhaopinList.forEach(function(item1, index3) {
                gwList += item1.type + "  招聘人数:" + item1.quantity + "人\n	";
              });
              listZG.value.push({
                videoId: listZG.value.length + 1,
                title: "招聘通知:\n	地址:" + item.zg_address + "\n	姓名:" + item.zg_name + "\n	联系电话:" + item.zg_tel + "\n	招聘信息:\n	" + gwList,
                poster: "https://img1.baidu.com/it/u=1314619043,374926406&fm=253&fmt=auto?w=348&h=500",
                //'https://i02piccdn.sogoucdn.com/2acf176d90718d73",
                url: apis.Url.baseUrlBiz + "/" + item.zg_mat_path,
                uploadTime: "2024-10-02 09:41",
                ipLocation: "北京",
                author: {
                  authorId: 102,
                  avatar: "https://i02piccdn.sogoucdn.com/2acf176d90718d73",
                  nickName: "管理员",
                  genderName: "女",
                  tel: item.zg_tel
                }
              });
            }
          });
        }
        formatAppLog("log", "at pagesSub/pages/adv/index.nvue:669", listFH.value);
      }).bind(this));
    };
    const change = (e) => {
      formatAppLog("log", "at pagesSub/pages/adv/index.nvue:814", "?? ~ file: index.vue:53 ~ change ~ data:", e);
    };
    const state = reactive({
      videoList: [
        {
          src: "https://vjs.zencdn.net/v/oceans.mp4",
          id: "1"
        },
        {
          src: "https://www.w3schools.com/html/movie.mp4",
          id: "2"
        },
        {
          src: "https://media.w3.org/2010/05/sintel/trailer.mp4",
          id: "3"
        },
        {
          src: "https://vjs.zencdn.net/v/oceans.mp4",
          id: "4"
        },
        {
          src: "https://www.w3school.com.cn/example/html5/mov_bbb.mp4",
          id: "5"
        }
      ]
    });
    const __returned__ = { store, user, roleID, teamID, get current() {
      return current;
    }, set current(v) {
      current = v;
    }, bgColor, lineColor, tabs, effect3d, effect3dMargin, autoplay, vertical, fullScreen, topFloat, fotterFloat, mode, indicatorPos, get listFH() {
      return listFH;
    }, set listFH(v) {
      listFH = v;
    }, get listZG() {
      return listZG;
    }, set listZG(v) {
      listZG = v;
    }, get listSH() {
      return listSH;
    }, set listSH(v) {
      listSH = v;
    }, get currVideoId() {
      return currVideoId;
    }, set currVideoId(v) {
      currVideoId = v;
    }, get activeIndex() {
      return activeIndex;
    }, set activeIndex(v) {
      activeIndex = v;
    }, dotIndex, dotFloatIndex, ad, get styleName() {
      return styleName;
    }, set styleName(v) {
      styleName = v;
    }, groupList, get phone() {
      return phone;
    }, set phone(v) {
      phone = v;
    }, win, width, height, bottomStyle, realList, currentVedio, get context() {
      return context;
    }, set context(v) {
      context = v;
    }, counter, get windowWidth() {
      return windowWidth;
    }, set windowWidth(v) {
      windowWidth = v;
    }, get statusBarHeight() {
      return statusBarHeight;
    }, set statusBarHeight(v) {
      statusBarHeight = v;
    }, onchange, loadMore, play, pause, ended, error, waiting, videoClick, doubleClick, maskClick, getList, currPlay, changeEvent, handleAdClick, changeTab, resetEvent, searchEvent, publishEvent, getGroup, getAdv, change, state, ref, toRaw, onMounted, reactive, get onLoad() {
      return onLoad;
    }, get onShow() {
      return onShow;
    }, get onUnload() {
      return onUnload;
    }, get onNavigationBarButtonTap() {
      return onNavigationBarButtonTap;
    }, get Https() {
      return apis;
    }, get userInfoStore() {
      return userInfoStore;
    }, get storeToRefs() {
      return storeToRefs;
    } };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  const _component_ml_swiper = resolveEasycom(resolveDynamicComponent("ml-swiper"), __easycom_0);
  const _component_button = resolveComponent("button");
  return openBlock(), createElementBlock("scroll-view", {
    scrollY: true,
    showScrollbar: true,
    enableBackToTop: true,
    bubble: "true",
    style: { flexDirection: "column" }
  }, [
    createElementVNode("view", { class: "tabsviewContent" }, [
      createCommentVNode(` <v-tabs class="tabsview" v-model="current" :scroll="false" \r
		:tabs="tabs" :bgColor='bgColor' :lineColor="lineColor"\r
		@change="changeEvent"></v-tabs> `),
      createElementVNode("view", { class: "top_nav" }, [
        createElementVNode(
          "view",
          {
            style: normalizeStyle({ height: $setup.statusBarHeight })
          },
          null,
          4
          /* STYLE */
        ),
        createElementVNode("view", { class: "top_content" }, [
          createElementVNode("view", { class: "content_btn" }, [
            createElementVNode("view", {
              class: "content_item",
              onClick: _cache[0] || (_cache[0] = ($event) => $setup.changeEvent(0))
            }, [
              createElementVNode(
                "u-text",
                {
                  class: normalizeClass(["item_title", { "i_on": $setup.current === 0 }])
                },
                "发货",
                2
                /* CLASS */
              ),
              $setup.current == 0 ? (openBlock(), createElementBlock("view", {
                key: 0,
                class: "line_on"
              })) : (openBlock(), createElementBlock("view", { key: 1 }))
            ]),
            createElementVNode("view", {
              class: "content_item",
              onClick: _cache[1] || (_cache[1] = ($event) => $setup.changeEvent(1))
            }, [
              createElementVNode(
                "u-text",
                {
                  class: normalizeClass(["item_title", { "i_on": $setup.current === 1 }])
                },
                "招工",
                2
                /* CLASS */
              ),
              $setup.current == 1 ? (openBlock(), createElementBlock("view", {
                key: 0,
                class: "line_on"
              })) : (openBlock(), createElementBlock("view", { key: 1 }))
            ]),
            createElementVNode("view", {
              class: "content_item",
              onClick: _cache[2] || (_cache[2] = ($event) => $setup.changeEvent(2))
            }, [
              createElementVNode(
                "u-text",
                {
                  class: normalizeClass(["item_title", { "i_on": $setup.current === 2 }])
                },
                "接货",
                2
                /* CLASS */
              ),
              createElementVNode(
                "view",
                {
                  view: "",
                  class: normalizeClass({ "line_on": $setup.current === 2 })
                },
                null,
                2
                /* CLASS */
              )
            ])
          ]),
          createCommentVNode(' <view class="search">\r\n		      <image class="img" src="/static/search.png"></image>\r\n		    </view> ')
        ])
      ]),
      createCommentVNode(' 	<view class="search">\r\n			<view class="item search_item">\r\n				<input class="input search_item_input" v-model="styleName" type="text" placeholder="请输入关键字">\r\n				<button class="btn search_item_btn" @click="resetEvent">重置</button>\r\n				<button class="btn submit search_item_btn_submit" @click="searchEvent">搜索</button>\r\n			</view>\r\n		</view> ')
    ]),
    $setup.current == 0 ? (openBlock(), createElementBlock("view", {
      key: 0,
      class: "work_1"
    }, [
      createCommentVNode(' <view class="search">\r\n			<view class="item search_item">\r\n				<input class="input search_item_input" v-model="styleName" type="text" placeholder="请输入关键字">\r\n				<button class="btn search_item_btn" @click="resetEvent">重置</button>\r\n				<button class="btn submit search_item_btn_submit" @click="searchEvent">搜索</button>\r\n			</view>\r\n		</view> '),
      createCommentVNode(' <image class="imgV" src="https://img1.baidu.com/it/u=1314619043,374926406&fm=253&fmt=auto?w=348&h=500"></image> '),
      createCommentVNode(' <videoSwiper :ad-list="list4" :ad-height="800" :ad-radius="0" @handleAdClick="handleAdClick"></videoSwiper> '),
      createCommentVNode(" <zSwiper\r\n			:autoplay='autoplay'\r\n			:list='listFH'\r\n			:effect3d='effect3d' \r\n			:fullScreen='fullScreen' \r\n			:effect3dMargin='`${effect3dMargin}rpx`' \r\n			:vertical='vertical'\r\n			:topFloat='topFloat'\r\n			:fotterFloat='fotterFloat'\r\n			:mode='mode'\r\n			:indicatorPos='indicatorPos'\r\n			@currPlay='currPlay'\r\n			></zSwiper> "),
      createElementVNode("view", { class: "vedioContent" }, [
        createVNode(_component_ml_swiper, {
          videoList: $setup.listFH,
          width: $setup.width,
          height: $setup.height,
          bottomStyle: $setup.bottomStyle,
          onLoadMore: $setup.loadMore,
          onChange: $setup.onchange,
          onPlay: $setup.play,
          onPause: $setup.pause,
          onEnded: $setup.ended,
          onError: $setup.error,
          onWaiting: $setup.waiting,
          onVideoClick: $setup.videoClick,
          onDoubleClick: $setup.doubleClick,
          onMaskClick: $setup.maskClick
        }, {
          bottom: withCtx(({ video, index: index2 }) => [
            createCommentVNode(" 视频标题 "),
            video ? (openBlock(), createElementBlock(
              "u-text",
              {
                key: 0,
                class: "videoTitle"
              },
              toDisplayString(video == null ? void 0 : video.title),
              1
              /* TEXT */
            )) : createCommentVNode("v-if", true)
          ]),
          _: 1
          /* STABLE */
        }, 8, ["videoList", "width", "height"])
      ])
    ])) : createCommentVNode("v-if", true),
    $setup.current == 1 ? (openBlock(), createElementBlock("view", {
      key: 1,
      class: "work_1"
    }, [
      createCommentVNode(' <image class="imgV" src="https://img1.baidu.com/it/u=4232351783,2577170786&fm=253&fmt=auto?w=333&h=500"></image> '),
      createCommentVNode(" <zSwiper\r\n			:autoplay='autoplay'\r\n			:list='listZG'\r\n			:effect3d='effect3d' \r\n			:fullScreen='fullScreen' \r\n			:effect3dMargin='`${effect3dMargin}rpx`' \r\n			:vertical='vertical'\r\n			:topFloat='topFloat'\r\n			:fotterFloat='fotterFloat'\r\n			:mode='mode'\r\n			:indicatorPos='indicatorPos'\r\n			></zSwiper> "),
      createElementVNode("view", { class: "vedioContent" }, [
        createVNode(_component_ml_swiper, {
          videoList: $setup.listZG,
          width: $setup.width,
          height: $setup.height,
          bottomStyle: $setup.bottomStyle,
          onLoadMore: $setup.loadMore,
          onChange: $setup.onchange,
          onPlay: $setup.play,
          onPause: $setup.pause,
          onEnded: $setup.ended,
          onError: $setup.error,
          onWaiting: $setup.waiting,
          onVideoClick: $setup.videoClick,
          onDoubleClick: $setup.doubleClick,
          onMaskClick: $setup.maskClick
        }, {
          bottom: withCtx(({ video, index: index2 }) => [
            createCommentVNode(" 视频标题 "),
            video ? (openBlock(), createElementBlock(
              "u-text",
              {
                key: 0,
                class: "videoTitle"
              },
              toDisplayString(video == null ? void 0 : video.title),
              1
              /* TEXT */
            )) : createCommentVNode("v-if", true)
          ]),
          _: 1
          /* STABLE */
        }, 8, ["videoList", "width", "height"])
      ])
    ])) : createCommentVNode("v-if", true),
    $setup.current == 2 ? (openBlock(), createElementBlock("view", {
      key: 2,
      class: "work_1"
    }, [
      createCommentVNode(' <image class="imgV" src="https://img0.baidu.com/it/u=1435639120,2241364006&fm=253&fmt=auto&app=138&f=JPEG?w=800&h=500"></image> '),
      createCommentVNode(" <zSwiper\r\n			:autoplay='autoplay'\r\n			:list='listSH'\r\n			:effect3d='effect3d' \r\n			:fullScreen='fullScreen' \r\n			:effect3dMargin='`${effect3dMargin}rpx`' \r\n			:vertical='vertical'\r\n			:topFloat='topFloat'\r\n			:fotterFloat='fotterFloat'\r\n			:mode='mode'\r\n			:indicatorPos='indicatorPos'\r\n			></zSwiper> "),
      createElementVNode("view", { class: "vedioContent" }, [
        createVNode(_component_ml_swiper, {
          videoList: $setup.listSH,
          width: $setup.width,
          height: $setup.height,
          bottomStyle: $setup.bottomStyle,
          onLoadMore: $setup.loadMore,
          onChange: $setup.onchange,
          onPlay: $setup.play,
          onPause: $setup.pause,
          onEnded: $setup.ended,
          onError: $setup.error,
          onWaiting: $setup.waiting,
          onVideoClick: $setup.videoClick,
          onDoubleClick: $setup.doubleClick,
          onMaskClick: $setup.maskClick
        }, {
          bottom: withCtx(({ video, index: index2 }) => [
            createCommentVNode(" 视频标题 "),
            video ? (openBlock(), createElementBlock(
              "u-text",
              {
                key: 0,
                class: "videoTitle"
              },
              toDisplayString(video == null ? void 0 : video.title),
              1
              /* TEXT */
            )) : createCommentVNode("v-if", true)
          ]),
          _: 1
          /* STABLE */
        }, 8, ["videoList", "width", "height"])
      ])
    ])) : createCommentVNode("v-if", true),
    createVNode(_component_button, {
      class: "btn submit_deploy",
      style: { "z-index": "9998" },
      onClick: $setup.publishEvent
    }, {
      default: withCtx(() => [
        createTextVNode("发 布")
      ]),
      _: 1
      /* STABLE */
    })
  ]);
}
const index = /* @__PURE__ */ _export_sfc(_sfc_main, [["render", _sfc_render], ["styles", [_style_0]], ["__file", "C:/Users/<USER>/Desktop/打印机/App源码/App/pagesSub/pages/adv/index.nvue"]]);
export {
  index as default
};
