<template>
	<swiper class="swiper" circular autoplay v-if="data.length">
		<swiper-item v-for="item in data" :key="item.ID">
			<image class="banner" :src="item.mediaUrl" @click="handleOpen(item.url)"></image>
		</swiper-item>
	</swiper>
</template>

<script setup>
const props = defineProps({
	data: {
		type: Array,
		default: []
	}
})

const handleOpen = (url) => {
	return;
	plus.runtime.openWeb(url)
}
</script>

<style lang="scss" scoped>
.swiper {
	.banner {
		display: block;
		width: 100%;
		height: 150px;
	}
}
</style>