import request from './https.js'
import requestNew from './httpsNew.js'
import requestFile from './httpsFile.js'
import userInfoStore from '@/store/userInfo.js'
import requestNewWithoutNotion from './httpsNewWithoutNotion.js'

const apis = {
	AppUser: {
		Login: data => _http('appUser/login', data),
		Register: data => _http('appUser/register', data),
		CheckExist: data => _http('appUser/checkExist', data, 'GET'),
		UserList: data => _http('appUser/getUserList', data, 'GET'),
		CheckUserStatus: data => _http1('Clothing/User/CheckUserStatus', data, 'POST'),
		AddLoginLog: data => _http1('Clothing/Staff/AddLoginLog', data, 'POST'),
		ModifyPwd: data => _http1('Clothing/Staff/ModifyPwd', data, 'POST'),
		AddMember: data => _http1('Clothing/User/AddMember', data, 'POST'),
		CheckUserAuth: data => _http1('Clothing/User/CheckUserAuth', data, 'POST'),
		CheckCheckMemberStatus: data => _http1('Clothing/User/CheckCheckMemberStatus', data, 'POST'),
	},
	AppRole: {
		List: () => _http('appRole/getAppRoleList', null, 'GET')
	},
	Company: {
		List: () => _http('company/getCompanyList', null, 'GET'),
		Join: data => _http('company/joinCompany', data),
		Check: data => _http('companyApply/optApply', data, 'PUT'),
		Apply: data => _http('companyApply/findCompanyApply', data, 'GET'),
		DelUser: data => _http('company/deleteStaff', data, 'DELETE')
	},
	Team: {
		List: data => _http('team/getTeamList', data, 'GET'),
		Join: data => _http('team/joinTeam', data),
		ApplyDetail: data => _http('teamApply/findTeamApply', data, 'GET'),
		Check: data => _http('teamApply/optApply', data, 'PUT'),
		DelUser: data => _http('team/deleteMember', data, 'DELETE')
	},
	Staff: {
		GroupList: data => _http1('Clothing/Staff/GroupList', data, 'POST'),
		UserList: data => _http1('Clothing/Staff/UserList', data, 'POST'),
		RoleList: () => _http1('Clothing/Staff/RoleList', null, 'POST'),
		AddGroup: data => _http1('Clothing/Staff/AddGroup', data, 'POST'),
		AddTeam: data => _http1('Clothing/Staff/AddTeam', data, 'POST'),
		EditTeam: data => _http1('Clothing/Staff/EditTeam', data, 'POST'),
		DeleteTeam: data => _http1('Clothing/Staff/DeleteTeam', data, 'POST'),
		UpdateGroup: data => _http1('Clothing/Staff/UpdateGroup', data, 'POST'),
		DeleteGroup: data => _http1('Clothing/Staff/DeleteGroup', data, 'POST'),
		ExistCompany: data => _http1('Clothing/Staff/ExistCompany', data, 'POST'),
		AddCompany: data => _http1('Clothing/Staff/AddCompany', data, 'POST'),
		UserRoleList: data => _http1('Clothing/Staff/UserRoleList', data, 'POST'),
		UserListByComp: data => _http2('Clothing/Staff/UserListByComp', data, 'POST'),
	},
	CroppingRecord: {
		List: data => _http('croppingRecord/getCroppingRecordList', data, 'GET'),
		Detail: data => _http('croppingRecord/findCroppingRecord', data, 'GET'),
		Update: data => _http('croppingRecord/updateCroppingRecord', data, 'PUT'),
		Add: data => _http('croppingRecord/createCroppingRecord', data),
		AddNew: data => _http1('Clothing/CroppingRecord/AddCroppingRecord', data, 'POST'),
		UpdatePrintStatus: data => _http1('Clothing/CroppingRecord/UpdatePrintStatus', data, 'POST'),
		GetPrintStatus: data => _http1('Clothing/CroppingRecord/GetPrintStatus', data, 'POST')
	},
	Style: {
		List: data => _http('style/getStyleList', data, 'GET'),
		Add: data => _http('style/createStyle', data),
		Detail: data => _http('style/findStyle', data, 'GET'),
		Update: data => _http('style/updateStyle', data, 'PUT'),
		DeleteStyle: data => _http1('Clothing/Process/DeleteStyle', data, 'POST'),
		StyleList: data => _http1('Clothing/CroppingRecord/StyleList', data, 'POST')
	},
	Cloth: {
		List: data => _http('cloth/getClothList', data, 'GET'),
		Add: data => _http('cloth/creatCloth', data),
		Detail: data => _http('cloth/findCloth', data, 'GET'),
		Update: data => _http('cloth/updateCloth', data, 'PUT'),
		AddCloth: (data) => _http1('Clothing/Cloth/AddCloth', data, 'POST'),
		UpdateCloth: (data) => _http1('Clothing/Cloth/UpdateCloth', data, 'POST'),
		ClothListByPage: (data) => _http1('Clothing/Cloth/ClothListByPage', data, 'POST'),
	},
	Wallet: {
		List: data => _http('userWallet/getUserWalletList', data, 'GET'),
		My: () => _http('userWallet/getMyWalletList', null, 'GET'),
		QueryWallet: (data) => _http1('Clothing/UserWallet/QueryWallet', data, 'POST'),
		ExportWallet: (data) => _http1('Clothing/UserWallet/Export2', data, 'POST'),
		WageSettle: (data) => _http1('Clothing/UserWallet/WageSettle', data, 'POST'),
		WageSettleCancel: (data) => _http1('Clothing/UserWallet/WageSettleCancel', data, 'POST')
	},
	Job: {
		List: data => _http('jobQuestion/getJobQuestionList', data, 'GET'),
		Detail: data => _http('job/findJob', data, 'GET'),
		Question: data => _http('jobQuestion/findJobQuestion', data, 'GET'),
		Update: data =>_http('jobQuestion/handleJobQuestion', data, 'PUT'),
		Add: data => _http('jobQuestion/createJobQuestion', data),
		Job: data => _http('job/getJobList', data, 'GET'),
		Process: data => _http('job/getJobGroupByProcess', data, 'GET'),
		ToApply: data => _http('job/jobAuditApply', data, 'PUT'), // 更改job表实际完成数量及金额
		Check: data => _http('job/jobAuditOpt', data, 'PUT'),  //审核  修改 job表记录的状态 2，4
		ChangeWorker: data => _http('job/changeWorker', data, 'PUT'),
		Task: data => _http('job/postJobList', data),  //添加 job 表记录
		Wages: data => _http('job/getWagesDetail', data, 'GET'),
		Apply: {
			Detail: data => _http('jobApply/findJobApply', data, 'GET'),
			Add: data => _http('jobApply/createJobApply', data),
			Check: data => _http('jobApply/optApply', data, 'PUT')
		},
		JobReceiveList: data => _http1('Clothing/Job/JobReceiveList', data, 'POST'),
		JobReceiveDetail: data => _http1('Clothing/Job/JobReceiveDetail', data, 'POST'),
		UpdateJobStatus: data => _http1('Clothing/Job/UpdateJobStatus', data, 'POST'),
		DeleteJob: data => _http2('Clothing/Job/DeleteJob', data, 'POST'),
		AddJob: data => _http1('Clothing/Job/AddJob', data, 'POST'),
		AddJobForOther: data => _http1('Clothing/Job/AddJobForOther', data, 'POST'),
		AddJobAll: data => _http1('Clothing/Job/AddJobAll', data, 'POST'),
		JobFinishList: data => _http2('Clothing/Job/JobFinishList', data, 'POST'),
		JobReceiveListByCroppingRecord: data => _http2('Clothing/Job/JobReceiveListByCroppingRecord', data, 'POST'),
		
	},
	Inventory: {
		Stock: data => _http('inventory/getInventoryList', data, 'GET'),
	},
	Process: {
		List: data => _http('process/getProcessList', data, 'GET'),
		ListNew: data => _http1('Clothing/Process/ProcessListNew', data, 'POST'),
		Detail: data => _http('process/findProcess', data, 'GET'),
		Add: data => _http('process/createProcess', data),
		Update: data => _http('process/updateProcess', data, 'PUT'),
		UpdateProcess: data => _http1('Clothing/Process/UpdateProcess', data, 'POST'),
		AddProcess: data => _http1('Clothing/Process/AddProcess', data, 'POST'),
		ProcessList: data => _http1('Clothing/Process/ProcessList', data, 'POST'),
		ProcessListByPage: data => _http1('Clothing/Process/ProcessListByPage', data, 'POST'),
		GetSizeQuanlity: data => _http1('Clothing/Process/GetSizeQuanlity', data, 'POST'),
		GetList: data => _http1('Clothing/Process/StyleList', data, 'POST'),
		GetStandard: data => _http1('Clothing/Process/GetStandard', data, 'POST'),
		AddStandard: data => _http2('Clothing/Process/AddStandard', data, 'POST'),
		GetMyStandard: data => _http2('Clothing/Process/GetMyStandard', data, 'POST'),
		StyleSettle: data => _http1('Clothing/Process/StyleSettle', data, 'POST'),	
		StyleSettleCancel: data => _http1('Clothing/Process/StyleSettleCancel', data, 'POST'),	
	},
	Chat: {
		GroupList: data => _http1('IM/Group/GroupList', data, 'POST'),
		AddGroup: data => _http2('IM/Group/AddGroup', data, 'POST'),
		CreateGroup: data => _http1('IM/Group/CreateGroup', data, 'POST'),
		GetUser: data => _http1('IM/Chat/GetUser', data, 'POST'),
	}, 
	Message: {
		List: data => _http('msgBox/getMyMsgBoxList', data, 'GET'),
		Send: data => _http('msgBox/getMySendMsgList', data, 'GET'),
		Read: data => _http('msgBox/setRead', data, 'GET')
	},
	Banner: {
		List: () => _http('banner/getBannerList', null, 'GET'),
		Find: data => _http('banner/findBanner', data, 'GET')
	},
	Computation: {
		Do: data => _http('computation/doComputation', data)
	},
	Order: {
		List: data => _http('order/getOrderList', data, 'GET'),
		Detail: data => _http('order/findOrder', data, 'GET'),
		Goods: () => _http('rechargeOption/getRechargeOptionList', null, 'GET'),
		Add: data => _http('order/createOrder', data),
		Pay: data => _http('order/payOrder', data)
	},
	Common:{
		Request: (uri,data,method) => _http(uri,data,method),
		Request1: (uri,data,method) => _http1(uri,data,method),
		GetInfo: data => _http1('Clothing/Common/GetInfo', data, 'POST'),
		AddColors: data => _http1('Clothing/Common/AddColors', data, 'POST'),
		GetColors: data => _http1('Clothing/Common/GetColors', data, 'POST')
	},
	Question:{
		QuestionList: data => _http1('Clothing/Job/QuestionList', data, 'POST'),
		ReplyQuestion: data => _http1('Clothing/Job/ReplyQuestion', data, 'POST'),
	},
	Adv:{
		AdvList: data => _http2('ADV/AdvInfo/AdvList', data, 'POST'),
		AddAdv: data => _http2('ADV/AdvInfo/AddAdv', data, 'POST'),
		DeleteAdv: data => _http1('ADV/AdvInfo/DeleteAdv', data, 'POST'),
		PayAdv: data => _http1('ADV/AdvInfo/PayAdv', data, 'POST'),
		BigList: data => _http2('ADV/AdvInfo/BigList', data, 'POST'),
		SmallList: data => _http2('ADV/AdvInfo/SmallList', data, 'POST'),
		BuyList: data => _http1('ADV/AdvInfo/BuyList', data, 'POST'),
		AdvDetial: data => _http1('ADV/AdvInfo/AdvDetail', data, 'POST'),
	},
	Url:{
		
		baseUrl:'http://*********:8889',
		baseUrlBiz:'http://*********:8890',
		/*
		// baseUrlBiz:'http://localhost:8890',
		*/
	   //baseUrl:'https://svapi.ruiruicaikuai.com',  //小程序
	   //baseUrlBiz:'https://wxapi.ruiruicaikuai.com',  //小程序
	   updateUrl :"https://wxapi.ruiruicaikuai.com/"  ,
	   baseUrlFileUpload:'https://wxapi.ruiruicaikuai.com/api/Files/Upload',
	},
	App:{
		vuex_version: "1.3.7",
		agent_code:""
		// agent_code:"CE3B60E851FF4CAC80B56FE8EAE4505F"//代理商 温s
		// agent_code:"B1305630EB3D4BC89DD2B51B6483684D"//18632699270  河北永清
		// agent_code:"052A54961CCC41F094F68A3752750C28"//河北沧州  鲍鱼哥
		// agent_code:"23FC437A08F444DFB94253BE20D9461F"//河北永清 赵喜波
		// agent_code:"0C65033E5FAC4F15A3A384CC256456C5"//河北永清 柴国祥
		// agent_code:"A3460E0FC268458C85C9AEF42AF5F64C"//河北沧州 阿军 
		// agent_code:"15A21B2B6B5E4420989FFDEFE0C7D695"//河北永清 刘中林
		
		// agent_code:"68DB2E4F11854FF3A8F969E7B5206B0E"//浙江杭州 刘学
		// agent_code:"517E7BA8811449CCAC299119A79B203C"//广州 欣欣女装
		// agent_code:"780C3DDAF0094C2CBA192B5565693D1F"//河北永清 邹雅帅
		
		/*
		18632699270	18632699270	河北永清	B1305630EB3D4BC89DD2B51B6483684D
		鲍鱼哥	13301257798	河北沧州	052A54961CCC41F094F68A3752750C28
		赵喜波	15776020343	河北永清	23FC437A08F444DFB94253BE20D9461F
		柴国祥	19943728940	河北永清	0C65033E5FAC4F15A3A384CC256456C5
		阿军 	17094751255	河北沧州	A3460E0FC268458C85C9AEF42AF5F64C
		刘中林	13260072678	河北永清	15A21B2B6B5E4420989FFDEFE0C7D695
		
		刘学	  	18967138776	浙江杭州	68DB2E4F11854FF3A8F969E7B5206B0E
		欣欣女装	13189018689	广州		517E7BA8811449CCAC299119A79B203C
		邹雅帅	13522291922	河北永清	780C3DDAF0094C2CBA192B5565693D1F

		*/
	}
}
  const baseUrl = 'http://*********:8889'
  const baseUrlBiz = 'http://*********:8890' //线上环境
 //const baseUrl = 'https://svapi.ruiruicaikuai.com'  //小程序
 //const baseUrlBiz = 'https://wxapi.ruiruicaikuai.com' //线上环境 小程序
 
const _http = (_url, data, method = 'POST') => {  
	const { token } = userInfoStore()
	let url=baseUrl + '/api/' + _url
	return request(url, data, {
		header: {
			'x-token': token
		},
		method
	})
}
const _http1 = (_url, data, method = 'POST') => {
	const { token } = userInfoStore()
	let url=baseUrlBiz + '/api/' + _url
	return requestNew(url, data, {
		header: {
			//'x-token': token
		},
		method
	})
}
const _http2 = (_url, data, method = 'POST') => {
	const { token } = userInfoStore()
	let url=baseUrlBiz + '/api/' + _url
	return requestNewWithoutNotion(url, data, {
		header: {
			//'x-token': token
		},
		method
	})
}
const _httpFile = (_url, data, method = 'POST') => {
	const { token } = userInfoStore()
	let url=baseUrlBiz + '/api/' + _url
	return requestFile(url, data, {
		header: {
			//'x-token': token
		},
		method
	})
}
export default apis