<template>
	<movable-area class="issue">
		<movable-view class="target" :x="x" :y="y" inertia direction="all">
			<image class="icon" src="../../static/issue.png" @click="handlePhone"></image>
		</movable-view>
	</movable-area>
</template>

<script setup>
import { ref } from 'vue'

const x = ref(10)
const y = ref(700)
uni.getSystemInfo({
	success({ windowWidth, windowHeight }) {
		x.value = windowWidth - 60
		y.value = windowHeight - 120
	}
})
const handlePhone = () => {
	uni.makePhoneCall({
		phoneNumber: '17533628519'
	})
}
</script>

<style lang="scss" scoped>
.issue {
	position: fixed;
	top: var(--window-top);
	bottom: var(--window-bottom);
	left: 0;
	right: 0;
	width: 100vw;
	height: calc(100vh - var(--window-top) - var(--window-bottom));
	pointer-events: none;
	.target {
		pointer-events: all;
		pointer-events: x;
		// width: 90rpx;
		// height: 90rpx;
		width: 0rpx;
		height: 0rpx;
		.icon {
			display: block;
			width: 100%;
			height: 100%;
		}
	}
}
</style>