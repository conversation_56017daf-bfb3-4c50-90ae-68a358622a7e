<template>
	<movable-area class="issue">
		<movable-view class="target" :x="xx" :y="yy" inertia direction="all">
			<view class="icon" @tap="emits('change')">
				<text :class="props.icon"></text>
			</view>
		</movable-view>
	</movable-area>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const emits = defineEmits(['change'])
const props = defineProps({
	x: Number,
	y: Number,
	icon: {
		type: String,
		default: 'icon-add'
	}
})

const xx = ref(10)
const yy = ref(700)
onMounted(() => {
	uni.getSystemInfo({
		success({ windowWidth, windowHeight }) {
			if (props.x) {
				xx.value = props.x
			} else {
				// xx.value = windowWidth - 60
			}
			if (props.y) {
				yy.value = props.y
			} else {
				yy.value = windowHeight - 120
			}
		}
	})
})

</script>

<style lang="scss" scoped>
.issue {
	position: fixed;
	top: var(--window-top);
	bottom: var(--window-bottom);
	left: 0;
	right: 0;
	width: 100vw;
	height: calc(100vh - var(--window-top) - var(--window-bottom));
	pointer-events: none;
	.target {
		pointer-events: auto;
		pointer-events: x;
		width: 90rpx;
		height: 90rpx;
		border-radius: 50%;
		background-color: $uni-color-primary;
		.icon {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 90rpx;
			height: 90rpx;
			color: $uni-text-color-inverse;
		}
	}
}
</style>