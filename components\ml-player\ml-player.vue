<template>

  <video class="ml-player" v-if="loadPlayer && video && video.url" id="ml-player" ref="mlPlayer" :src="video.url"
    :poster="video.poster" :title="video.title" :controls="options.controls" :autoplay="options.autoplay"
    :loop="options.loop" :muted="options.muted" :initial-time="options.initialTime" :duration="options.duration"
    :show-fullscreen-btn="options.controls" :show-play-btn="options.controls"
    :show-center-play-btn="options.showCenterPlayBtn" :show-loading="options.showLoading"
    :enable-progress-gesture="options.enableProgressGesture" :object-fit="options.objectFit"
    :show-mute-btn="options.controls" :play-btn-position="options.playBtnPosition"
    :auto-pause-if-navigate="options.autoPauseIfNavigate" :auto-pause-if-open-native="options.autoPauseIfOpenNative"
    :vslide-gesture="options.vslideGesture" :vslide-gesture-in-fullscreen="options.vslideGestureInFullscreen"
    :codec="options.codec" :http-cache="options.httpCache" :play-strategy="options.playStrategy"
    :show-progress="options.showProgress" :page-gesture="options.vslideGesture"
    :mobilenet-hint-type="options.mobilenetHintType" :enable-play-gesture="options.enablePlayGesture"
    :is-live="options.isLive" @click.stop="doubleClick" @play="play" @pause="pause" @ended="ended"
    @timeupdate="timeupdate" @fullscreenchange="fullscreenchange" @waiting="waiting" @error="videoError"
    @progress="progress" @loadedmetadata="loadedmetadata" @fullscreenclick="fullscreenclick"
    @controlstoggle="controlstoggle" webkit-playsinline="true" playsinline="true" x-webkit-airplay="allow"
    x5-video-player-type="h5-page" x5-video-orientation="portrait"
    :style="{width: width + 'px', height: height + 'px'}">

    <cover-view class="rateIcon" :style="{top: (height / 2) + 'px'}" v-if="options.showRate || options.showFit">
      <view v-if="options.showRate" @click.stop="openRate">
        <text class="rateText">{{ rate }}×</text>
      </view>
      <view v-if="options.showFit" style="margin-top: 2px;" @click.stop="openFit">
        <text class="rateText" style="font-size: 17px;line-height: 17px;">⚙</text>
      </view>
    </cover-view>

    <cover-view>
      <slot></slot>
    </cover-view>

    <view class="center-play-mask" v-if="!playing || loading" :style="{width: width + 'px', height: winHeight + 'px'}">
      <cover-image v-if="!loading && !playing"
        src="data:image/png;base64,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"
        class="center-play-btn" @click.stop="playVideo"></cover-image>
      <cover-image v-else-if="loading"
        src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAAAXNSR0IArs4c6QAABX9JREFUaEPtWVuME2UU/s5MFwFFCRh4gMJ2irLMbNspm0B0o/IgoIaoGORBMQZFY6IRlPhiRMVLiAleow9GhA1GgkYMvmi8BWIIKAE63e502Ww7Xa4KSiSCLnbbOTKlXdo67XR70Uqcx3/O+c73/ef/z38j1Pj5+8NTRdG1mBmnNK+ypRhuXiIx+rT5+zoQ3QtgCMAmTVKeqTHssDvVAjQ71tthCua3AK7K4miDSfHGvra2Mzlc1dA/ALAsPw4Db4UlZWUtsXO+NQlQDX3gfI9OLyTCGzSp/SGrzRfv9YlkdtsR5RbX1LB75rFaRVQtoKM/4k2LQsyGgKFJitdqV+P6PSB8aEeSmOaHvPI31r+ZBw+OG9uSWsSC4AbSezWPb2elwqoXcHzf2PS50b8ANKYwGH2lSfJCq609FnG7BOGwDZmzoKRb8wRPB/p1hQT+EkRTcnYMbAtLypJKRDgKyPbiUgZiLoFe3d8q/5gDDhrR+xi8OS/QbybEOd1SW9/wHEhEV4H59QIyhOWaR+my2gKG3ktAWzFZBq0JS/JLTiLKCggY+lICPsqbMJGQpPjzQYMD+q1gWmFVIVMQ1nW3tiWKg6oJ/Q4wloGQBFOXJslfWzZ+Q79BAL4rQXJ4KJYTUVaAakTDABcQBmhBjoBT7zj9D8ajNzNxRozNd1yTlOFhVQrLQYB+AEAw3zl/8jkRdPo/t7//yj/FpFWJrvibLaNL8yrLnTCcBNwN4ONyQ8gpgNP/wEB0MZn8aZHdobSYDkam+3918necxAFDX0bAErtJ7ARe6f/sgrgWwGxi2vzHkPCytRh29PVdnXalFkKgKTDRo3nlz4sxHQVUSqLedrMT+i0mY2veKg8CIkMw5/dIvhN5o6LeoWvHU47oE1qG0A9ggg3aJ5qkWEM78w1n4EJJo7fB5kSQ8LwmyRtqp1IdQtDQVzOwvpR3yjSn9czwHSkQULyvyTeqjkb1XqoRfRvgR0uXTropJMmZ9SOTATXW0wmBdhU4ED2heeQ3qqdRvWe2cFi7WNsvOYhxUUU5ezEDzIKaiFobM0/WwySX6A5NaztePY3qPeft2OE63TpJA0MpRmHGC2Gv8lzpOQCeDPAaTWp/r3oKtXv6DnVLYlq09lmdF9HoHU2SH8tHb9oymiPpT+hzBZM6qEXYbjciml5ATohqROeDuZNAJ1KgXRHvrEhBFao96Y1DUI1oF8D3F0V4SpOU9U2fgUBcX0uEZ227h3BnRoA/Fp4ExqjuawJHG9ePI0duNyKTXRB+KunJfIwKdoN5J6WRh6u/R4mdauFypRq6dYJqzbaeTA7CHVWUZP3pjBwxU4EY35fxNElN6DEwMrcIAH4eT2On7fR4zo08XGM81LiugyDboTPzVgrEI3cRCdssA2Y8EPYqmxpDpTrUzK2FiB8AXF6EcDgtptWmr0IW6ayIjQDmZEQwtqfIfMQ6F/wnBJTL3f8CqhvZ9fO6tDJgXSMS6GTu0rV+/dQ4pOEMBAz9TQIet0IRaEVIkt9vXNj6IecJ6NlHoI6MAMLGkEd5sH5hGoeUL+BhAr2bCWWandoM3+7Gha0fcsEktg73gsBnDkg+21eV+oWtHGnO0d6JQylcG2qdtcfO69KqQpX3S/NYXvoZsE5FLRAXjW4Zt2WP2z3YPH1/gUnZDMi6PuqyMYgx4D7/ZldwqdosQhyHkGro1iPDeDC+0LzKbY0iblWbvVNnnRopvqOAgBFZQCxcLwrprfs9/oMjDVCpvRrveQUC9eZeLyv1cxRQKdC/ZVezgOAhXTbTeJLN1NPdMwIn/2khNQtQDf0zALcz6MWwJNteQAWO9E2hodRrZjq1ut53T7ULyDxi0yoB6ZWltiDBgd7r2DR3N2KP9RdxLfhyI6cVtQAAAABJRU5ErkJggg=="
        class="center-loading">
      </cover-image>
      <text style="color: #fff;text-align: center;" v-if="!playing || loading">点击播放</text>
    </view>
  </video>

  <image v-if="!loadPlayer && options.showPoster && video.poster" :src="video.poster"
    :style="{width: width + 'px', height: height + 'px'}"></image>
</template>

<script setup>
  /**
   * ml-player 陌路 播放器插件
   * @description 播放器
   * @property {Boolean} showPlayer 是否显示播放器
   * @property {Object} video 视频资源信息
   * @property {Object} danmu 弹幕配置
   * @property {Object} videoOptions 播放器配置
   * 
   * @event {Function} play 视频播放时 触发此事件
   * @event {Function} pause 视频暂停时 触发此事件
   * @event {Function} ended 视频播放结束时 触发此事件
   * @event {Function} error 视频播放出错时 触发此事件
   * @event {Function} waiting 视频出现缓冲时 触发此事件
   * @event {Function} timeupdate 进度条变化时 触发此事件
   * @event {Function} videoClick 点击 video 触发此事件
   * @event {Function} doubleClick 双击 video 触发此事件，APP端不支持
   * 
   * @event {Function} fullscreenchange 当视频进入和退出全屏时 触发此事件
   * @event {Function} progress 加载进度变化时 触发此事件
   * @event {Function} loadedmetadata 视频元数据加载完成时 触发此事件
   * @event {Function} fullscreenclick 视频播放全屏播放时点击事件 触发此事件
   * @event {Function} controlstoggle 切换 controls 显示隐藏时 触发此事件
   */

  import { ref, getCurrentInstance, onUnmounted, computed } from 'vue';
  import { onReady, onHide, onUnload } from '@dcloudio/uni-app';

  defineOptions({ name: "MlPlayer" });

  const props = defineProps({
    // 显示播放器
    showPlayer: {
      type: Boolean,
      default: true
    },
    // 资源配置
    video: { // {url:"", poster:"", title:""}
      type: Object,
      default: {},
      required: true
    },
    // 弹幕配置
    danmu: {
      danmuList: { // 弹幕信息列表 { text: '第1s出现的弹幕，红色字体', color: '#ff0000', time: 1}
        type: Array,
        default: []
      },
      danmuBtn: { // 是否显示弹幕按钮，只在初始化时有效，不能动态变更
        type: Boolean,
        default: false
      },
      enableDanmu: { // 是否展示弹幕，只在初始化时有效，不能动态变更
        type: Boolean,
        default: false
      }
    },
    // 播放器插件配置
    videoOptions: {
      type: Object,
      default: {
        width: 0, // 组件宽度
        height: 0, // 组件高度
        fillHeight: false, // 是否填充高度
        controls: true, // 是否显示默认播放控件（播放/暂停按钮、播放进度、时间） 
        autoplay: true, // 是否自动播放
        loop: true, // 是否循环播放
        muted: false, // 是否静音播放
        initialTime: 0, // 指定视频初始播放位置 单位为秒（s）
        duration: 0, // 指定视频长度，单位为秒（s）
        showPoster: true, // 显示预览图
        showProgress: true, // 显示进度条
        // showFullscreenBtn: true, // 是否显示全屏按钮
        // showPlayBtn: false, // 是否显示视频底部控制栏的播放按钮
        showCenterPlayBtn: true, // 是否显示视频中间的播放按钮
        enablePlayGesture: false, // 是否开启播放手势，即双击切换播放/暂停
        showLoading: true, // 是否显示loading控件
        enableProgressGesture: true, // 是否开启控制进度的手势
        objectFit: "contain", // 当视频大小与 video 容器大小不一致时，视频的表现形式。contain：包含、fill：填充、cover：覆盖
        // showMuteBtn: false, // 是否显示静音按钮
        playBtnPosition: "center", // 播放按钮的位置：bottom、center
        mobilenetHintType: 1, // 移动网络提醒样式：0是不提醒，1是提醒，默认值为1
        autoPauseIfNavigate: true, // 微信：当跳转到其它小程序页面时，是否自动暂停本页面的视频
        autoPauseIfOpenNative: true, // 微信：当跳转到其它微信原生页面时，是否自动暂停本页面的视频 
        vslideGesture: true, // 在非全屏模式下，是否开启亮度与音量调节手势（同 page-gesture） 
        vslideGestureInFullscreen: true, // 在全屏模式下，是否开启亮度与音量调节手势
        codec: "hardware", // 解码器选择：hardware：硬解码（硬解码可以增加解码算力，提高视频清晰度。）；software：ffmpeg软解码；
        httpCache: true, // 是否对 http、https 视频源开启本地缓存 （不适用于m3u8等流媒体协议）
        /**
         * 播放策略：
         *  0-普通模式，适合绝大部分视频播放场景；
         *  1-平滑播放模式，增加缓冲区大小，采用open sl解码音频，避免音视频脱轨的问题，可能会降低首屏展现速度、视频帧率，出现开屏音频延迟等 适用于高码率视频的极端场景；
         *  2：M3U8优化模式，增加缓冲区大小，提升视频加载速度和流畅度，可能会降低首屏展现速度。 适用于M3U8在线播放的场景
         */
        playStrategy: 2, // 播放策略：0：普通模式、1：平滑播放模式、2：M3U8优化模式
        header: {}, // HTTP 请求 Header
        isLive: false, // 是否为直播源，App 3.7.2+、微信小程序（2.28.1+）
        showRate: true, // 是否显示倍速按钮
        showFit: true, // 是否显示展示形式
        rateList: ["0.5", "0.8", "1.0", "1.25", "1.5", "2.0"],// 视频倍速 
        enableClick: false,  // 是否启用视频点击事件
        enableDblClick: false,// 是否启用视频双击事件
        showWaitingTips: true,// 显示视频缓冲提示
        waitingCount: 5,// 缓冲次数等于该值时显示提示
        waitingMessage: "当前网络不佳"  // 网络缓存提示信息
      }
    }
  });
  const loading = ref(false);
  const winInfo = uni.getSystemInfoSync();
  const width = ref(props.videoOptions?.width || winInfo.windowWidth); // 设备宽度
  const height = ref(props.videoOptions?.height || Math.ceil((winInfo.windowHeight * 0.33))); // 设备高度
  const winHeight = ref(height.value);
  if (props.videoOptions?.fillHeight == true) {
    height.value = winInfo.windowHeight;
    winHeight.value = height.value;
  }

  const options = ref({
    width: width.value,
    height: height.value,
    controls: props.videoOptions?.controls != false,
    autoplay: props.videoOptions?.autoplay != false,
    loop: props.videoOptions?.loop != false,
    muted: props.videoOptions?.muted == true,
    initialTime: props.videoOptions?.initialTime ?? 0,
    duration: props.videoOptions?.duration ?? 0,
    showPoster: props.videoOptions?.showPoster != false,
    showProgress: props.videoOptions?.showProgress != false,
    // showFullscreenBtn: props.videoOptions?.showFullscreenBtn,
    // showPlayBtn: props.videoOptions?.showPlayBtn,
    // showCenterPlayBtn: props.videoOptions?.showCenterPlayBtn != false,
    showCenterPlayBtn: false,
    enablePlayGesture: props.videoOptions?.enablePlayGesture == true,
    showLoading: props.videoOptions?.showLoading != false,
    enableProgressGesture: props.videoOptions?.enableProgressGesture != false,
    objectFit: props.videoOptions?.objectFit || "contain",
    // showMuteBtn: props.videoOptions?.showMuteBtn,
    playBtnPosition: props.videoOptions?.playBtnPosition || "center",
    mobilenetHintType: props.videoOptions?.mobilenetHintType ?? 1,
    autoPauseIfNavigate: props.videoOptions?.autoPauseIfNavigate != false,
    autoPauseIfOpenNative: props.videoOptions?.autoPauseIfOpenNative != false,
    vslideGesture: props.videoOptions?.vslideGesture != false,
    vslideGestureInFullscreen: props.videoOptions?.vslideGestureInFullscreen != false,
    codec: props.videoOptions?.codec || "hardware",
    httpCache: props.videoOptions?.httpCache != false,
    playStrategy: props.videoOptions?.playStrategy ?? 2,
    header: props.videoOptions?.header || {},
    isLive: props.videoOptions?.isLive == true,
    showRate: props.videoOptions?.showRate != false,
    rateList: props.videoOptions?.rateList || ["0.5", "0.8", "1.0", "1.25", "1.5", "2.0"],
    enableClick: props.videoOptions?.enableClick == true,
    enableDblClick: props.videoOptions?.enableDblClick == true,
    showWaitingTips: props.videoOptions?.showWaitingTips != false,
    waitingCount: props.videoOptions?.waitingCount || 5,
    waitingMessage: props.videoOptions?.waitingMessage || "当前网络不佳",
    fillHeight: props.videoOptions?.fillHeight == true,
    showFit: props.videoOptions?.showFit != false
  });

  let instance = getCurrentInstance();
  let playerContext = null;
  const rate = ref(1);
  const counter = ref(0);
  const mlPlayer = ref(null);
  const playing = ref(false);

  const loadPlayer = computed(() => props.showPlayer != false);

  let lastTapDiffTime = 0;
  let timer = 0;

  const emits = defineEmits([
    'videoClick',
    'doubleClick',
    'play',
    'pause',
    'ended',
    'timeupdate',
    'error',
    'fullscreenchange',
    'waiting',
    'progress',
    'loadedmetadata',
    'fullscreenclick',
    'controlstoggle',
    'playVideo'
  ]);

  onReady(() => {
    if (!playerContext || playerContext == null) {
      setTimeout(() => {
        initVideoContext();
      }, 500);
    }
  });

  /**
   * 初始化视频组件
   */
  const initVideoContext = () => {
    const contextEmpty = (!playerContext || playerContext == null);
    const video = props.video || {};
    if (contextEmpty && video && video.url && video.url?.length > 0) {
      playerContext = uni.createVideoContext("ml-player", instance);
    }
    return playerContext;
  };

  /**
   * 视频 开始/继续 播放
   */
  const play = () => {
    initVideoContext();
    playing.value = true;
    loading.value = false;
    emits('play', playerContext);
  };

  /**
   * 视频暂停播放
   */
  const pause = () => {
    initVideoContext();
    playing.value = false;
    loading.value = false;
    emits('pause', playerContext);
  };

  /**
   * 视频播放结束
   */
  const ended = () => {
    initVideoContext();
    counter.value = 0;
    loading.value = false;
    emits('ended', playerContext);
  };

  /**
   * 点击播放
   */
  const playVideo = () => {
    initVideoContext();
    if (playerContext) {
      playerContext?.play();
      playing.value = true;
      loading.value = false;
    }
    emits('playVideo', playerContext);
  };

  /**
   * 播放进度变化时触发：触发频率 250ms 一次
   * @param {Object} event.detail = {currentTime, duration} 。
   */
  const timeupdate = (event) => {
    playing.value = true;
    loading.value = false;
    emits('timeupdate', playerContext, event);
  };

  /**
   * 当视频进入和退出全屏时触发
   * @param {Object} event
   */
  function fullscreenchange(event) {
    emits('fullscreenchange', playerContext, event);
  }

  /**
   * 视频出现等待缓冲
   */
  const waiting = () => {
    loading.value = true;
    if (options.value.showWaitingTips) {
      if (counter.value >= options.value.waitingCount) {
        showToast(options.value.waitingMessage, 'none');
        counter.value = 0;
        return false;
      }
      counter.value = counter.value + 1;
    }
    initVideoContext();
    emits('waiting', playerContext);
  };

  /**
   * 视频的展示形式
   */
  const openFit = () => {
    const fit = ["包含", "填充", "覆盖"];
    uni.showActionSheet({
      title: "设置展示形式",
      alertText: "选择展示形式",
      itemList: fit,
      success: function (res) {
        const index = res.tapIndex;
        const val = ["contain", "fill", "cover"][index];
        options.value.objectFit = val;
        showToast("展示形式：" + fit[index], 'none');
      }
    });
  };

  /**
   * 选择倍速
   */
  const openRate = () => {
    const rateList = options.value.rateList || ["0.5", "0.75", "1.0", "1.25", "1.5", "1.75", "2.0", "2.5"];
    if (rateList && rateList.length > 0) {
      uni.showActionSheet({
        title: "设置倍速",
        alertText: "选择倍速",
        itemList: rateList,
        success: function (res) {
          const rateNum = Number(rateList[res.tapIndex]);
          if (!playerContext) initVideoContext();
          if (playerContext && !isNaN(rateNum)) {
            rate.value = rateNum;
            playerContext?.playbackRate(rateNum);
            showToast("倍速：" + rateNum, 'none');
          }
        }
      });
    }
  };

  /**
   * 视频播放出错
   */
  const videoError = (event) => {
    showToast('资源播放错误', 'error');
    playing.value = false;
    loading.value = false;
    const errInfo = { video: props.video, error: event };
    console.error("==========资源播放错误=========");
    console.error(errInfo);
    emits('error', playerContext, event);
  };

  /**
   * 加载进度变化时触发
   */
  function progress(event) {
    emits('progress', playerContext, event);
  }

  /**
   * 视频元数据加载完成时触发。
   * @param {Object} event
   */
  function loadedmetadata(event) {
    emits('loadedmetadata', playerContext, event);
  }

  /**
   * 视频播放全屏播放时点击事件。
   * @param {Object} event
   */
  function fullscreenclick(event) {
    emits('fullscreenclick', playerContext, event);
  }

  /**
   * 切换 controls 显示隐藏时触发。
   * @param {Object} event
   */
  function controlstoggle(event) {
    emits('controlstoggle', playerContext, event);
  }

  /**
   * 视频单击事件
   */
  const videoClick = () => {
    if (!(options.value.enableClick == true)) return;
    initVideoContext();
    emits('videoClick', playerContext, props.video);
  };

  /**
   * 双击事件
   */
  const doubleClick = () => {
    let currentTime = Date.now();
    let lastTime = lastTapDiffTime;
    lastTapDiffTime = currentTime;
    if (currentTime - lastTime < 200) {
      clearTimeout(timer);
      if (options.value.enableDblClick == true) {
        emits('doubleClick', playerContext, props.video);
      }
    } else {
      timer = setTimeout(() => {
        videoClick();
      }, 200);
    }
  };

  /**
   * 显示消息提示
   */
  const showToast = (title, icon) => {
    uni.hideToast();
    uni.showToast({
      title: title,
      icon: icon || 'none',
      mask: false,
      duration: 2000
    });
  };

  /**
   * 重置变量
   */
  function resetVariables() {
    if (playerContext) {
      playerContext?.pause();
      playerContext?.stop();
    }

    clearTimeout(timer);

    playerContext = null;
    mlPlayer.value = null;
    options.value = null;
    instance = null;
  }

  /**
   * 页面隐藏，暂停播放
   */
  onHide(() => {
    if (playerContext) {
      playerContext?.pause();
    }
  });

  /**
   * 监听页面卸载
   */
  onUnload(() => {
    resetVariables();
  });

  /**
   * 页面销毁
   */
  onUnmounted(() => {
    resetVariables();
  });
</script>

<style scoped lang="scss">
	video{
	    width:100%;
	    height:100%;
	    object-fit:fill;  
	}
  .ml-player {
    position: relative;
    z-index: 0;
    /* #ifdef H5 */
    overflow: hidden;
    /* #endif */

    .rateIcon {
      position: absolute;
      right: 3px;
      top: 95px;
      z-index: 1;

      .rateText {
        font-size: 11px;
        color: #fff;
        background: rgba(0, 0, 0, 0.5);
        padding: 5rpx 8rpx;
        border-radius: 8rpx;
        height: 22px;
        line-height: 22px;
      }
    }

    .center-play-mask {
      position: fixed;
      /* #ifndef APP-NVUE */
      display: flex;
      /* #endif */
      top: 0;
      left: 0;
      align-items: center;
      justify-content: center;
      background: rgba(0, 0, 0, 0.2);

      .center-play-btn {
        // align-items: center;
        // justify-content: center;
        /* #ifndef APP-NVUE */
        // position: absolute;
        // display: flex;
        // top: 50%;
        // left: 50%;
        // transform: translate(-50%, -50%);
        /* #endif */
        width: 140rpx;
        height: 140rpx;
      }

      .center-loading {
        width: 100rpx;
        height: 100rpx;
        /* #ifndef APP-NVUE */
        animation: rotate 2s linear infinite;
        /* #endif */
      }

      /* #ifndef APP-NVUE */
      @keyframes rotate {
        from {
          transform: rotate(0deg);
        }

        to {
          transform: rotate(360deg);
        }
      }

      /* #endif */
    }
  }
</style>