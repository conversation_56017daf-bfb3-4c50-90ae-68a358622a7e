<template>
	<view id="popup" style="position: relative;width: 100%;height: 100%;">
		<view class="video_mask" @touchstart="touchStart" @touchend="touchEnd">

		</view>
		<view class="rowDot">
			<view v-for="(item , index) in swiperList" :key="index">
				<view :class="['dot', index === swiperCurrent ? 'dotActive' : '']"></view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				startTime: 0,
				startPosition: 0,
				endPosition: 0,
				swiperList: [],
				swiperCurrent: 0
			}
		},
		mounted() {
			console.log("mounted")
			let that = this
			uni.$on('init_param', (data) => {
				console.log(data)
				that.swiperList = data
			})
			uni.$on('init_current', (data) => {
				console.log(data)
				that.swiperCurrent = data
			})
		},
		methods: {
			adClick() {
				console.log('adClick')
				uni.$emit('adClick', this.swiperCurrent)
			},
			// 起点
			touchStart(event) {
				console.log('touchStart')
				this.startTime = Date.now()
				this.startPosition = event.changedTouches[0].clientX
				// #ifdef APP-PLUS
				this.startPosition = event.changedTouches[0].screenX
				// #endif
			},
			// 终点,计算移动距离
			touchEnd(event) {
				console.log('touchEnd')
				const endTime = Date.now()
				if (endTime - this.startTime > 2000) {
					return;
				}
				this.endPosition = event.changedTouches[0].clientX
				// #ifdef APP-PLUS
				this.endPosition = event.changedTouches[0].screenX
				// #endif
				var elePosition = ""
				//当移动距离超过10时判断左滑右滑。
				if (Math.abs(this.endPosition - this.startPosition) > 10) {
					elePosition = this.endPosition - this.startPosition > 0 ? "right" : "left"
				} else {
					//点击事件
					this.adClick()
					return;
				}

				//将左滑还是右滑，传到父页面
				console.log(elePosition)
				uni.$emit('swiperChangeDirection', {
					elePosition: elePosition
				});
			},
		}
	}
</script>

<style scoped lang="scss">
	@import "./liuliu-video-mask.scss";
</style>