"use weex:vue";

if (typeof Promise !== 'undefined' && !Promise.prototype.finally) {
  Promise.prototype.finally = function(callback) {
    const promise = this.constructor
    return this.then(
      value => promise.resolve(callback()).then(() => value),
      reason => promise.resolve(callback()).then(() => {
        throw reason
      })
    )
  }
};

if (typeof uni !== 'undefined' && uni && uni.requireGlobal) {
  const global = uni.requireGlobal()
  ArrayBuffer = global.ArrayBuffer
  Int8Array = global.Int8Array
  Uint8Array = global.Uint8Array
  Uint8ClampedArray = global.Uint8ClampedArray
  Int16Array = global.Int16Array
  Uint16Array = global.Uint16Array
  Int32Array = global.Int32Array
  Uint32Array = global.Uint32Array
  Float32Array = global.Float32Array
  Float64Array = global.Float64Array
  BigInt64Array = global.BigInt64Array
  BigUint64Array = global.BigUint64Array
};


(()=>{var at=Object.create;var ke=Object.defineProperty;var nt=Object.getOwnPropertyDescriptor;var it=Object.getOwnPropertyNames;var lt=Object.getPrototypeOf,st=Object.prototype.hasOwnProperty;var Le=(e,l)=>()=>(l||e((l={exports:{}}).exports,l),l.exports);var rt=(e,l,r,c)=>{if(l&&typeof l=="object"||typeof l=="function")for(let b of it(l))!st.call(e,b)&&b!==r&&ke(e,b,{get:()=>l[b],enumerable:!(c=nt(l,b))||c.enumerable});return e};var ie=(e,l,r)=>(r=e!=null?at(lt(e)):{},rt(l||!e||!e.__esModule?ke(r,"default",{value:e,enumerable:!0}):r,e));var xe=(e,l,r)=>new Promise((c,b)=>{var s=m=>{try{f(r.next(m))}catch(C){b(C)}},v=m=>{try{f(r.throw(m))}catch(C){b(C)}},f=m=>m.done?c(m.value):Promise.resolve(m.value).then(s,v);f((r=r.apply(e,l)).next())});var ce=Le((Lt,_e)=>{_e.exports=Vue});var pe=Le((xt,Be)=>{Be.exports=uni.Pinia});var t=ie(ce()),Ge=ie(pe()),ut="onShow",dt="onHide",ct="onLoad",pt="onReady",gt="onUnload",ht="onNavigationBarButtonTap";function T(e,l,...r){uni.__log__?uni.__log__(e,l,...r):console[e].apply(console,[...r,l])}function ge(e,l){return typeof e=="string"?l:e}var $=e=>(l,r=(0,t.getCurrentInstance)())=>{!t.isInSSRComponentSetup&&(0,t.injectHook)(e,l,r)},he=$(ut),Ie=$(dt),De=$(ct),vt=$(pt),Re=$(gt),Ue=$(ht),ft={"ml-player":{"":{position:"relative",zIndex:0}},rateIcon:{".ml-player ":{position:"absolute",right:3,top:95,zIndex:1}},rateText:{".ml-player .rateIcon ":{fontSize:11,color:"#ffffff",backgroundColor:"rgba(0,0,0,0.5)",paddingTop:"5rpx",paddingRight:"8rpx",paddingBottom:"5rpx",paddingLeft:"8rpx",borderRadius:"8rpx",height:22,lineHeight:22}},"center-play-mask":{".ml-player ":{position:"fixed",top:0,left:0,alignItems:"center",justifyContent:"center",backgroundColor:"rgba(0,0,0,0.2)"}},"center-play-btn":{".ml-player .center-play-mask ":{width:"140rpx",height:"140rpx"}},"center-loading":{".ml-player .center-play-mask ":{width:"100rpx",height:"100rpx"}}},le=(e,l)=>{let r=e.__vccOpts||e;for(let[c,b]of l)r[c]=b;return r},mt=Object.assign({name:"MlPlayer"},{__name:"ml-player",props:{showPlayer:{type:Boolean,default:!0},video:{type:Object,default:{},required:!0},danmu:{danmuList:{type:Array,default:[]},danmuBtn:{type:Boolean,default:!1},enableDanmu:{type:Boolean,default:!1}},videoOptions:{type:Object,default:{width:0,height:0,fillHeight:!1,controls:!0,autoplay:!0,loop:!0,muted:!1,initialTime:0,duration:0,showPoster:!0,showProgress:!0,showCenterPlayBtn:!0,enablePlayGesture:!1,showLoading:!0,enableProgressGesture:!0,objectFit:"contain",playBtnPosition:"center",mobilenetHintType:1,autoPauseIfNavigate:!0,autoPauseIfOpenNative:!0,vslideGesture:!0,vslideGestureInFullscreen:!0,codec:"hardware",httpCache:!0,playStrategy:2,header:{},isLive:!1,showRate:!0,showFit:!0,rateList:["0.5","0.8","1.0","1.25","1.5","2.0"],enableClick:!1,enableDblClick:!1,showWaitingTips:!0,waitingCount:5,waitingMessage:"\u5F53\u524D\u7F51\u7EDC\u4E0D\u4F73"}}},emits:["videoClick","doubleClick","play","pause","ended","timeupdate","error","fullscreenchange","waiting","progress","loadedmetadata","fullscreenclick","controlstoggle","playVideo"],setup(e,{emit:l}){var Pe,Se,Te,Oe;var r,c,b,s,v,f,m,C,A,P,O,S,q,X,R,z,M,j,V,J,Q,H,N,U,Z,K,u,g,w,o,k,_,x,ee,re;let d=e,B=(0,t.ref)(!1),ue=uni.getSystemInfoSync(),ae=(0,t.ref)(((r=d.videoOptions)==null?void 0:r.width)||ue.windowWidth),Y=(0,t.ref)(((c=d.videoOptions)==null?void 0:c.height)||Math.ceil(ue.windowHeight*.33)),fe=(0,t.ref)(Y.value);((b=d.videoOptions)==null?void 0:b.fillHeight)==!0&&(Y.value=ue.windowHeight,fe.value=Y.value);let p=(0,t.ref)({width:ae.value,height:Y.value,controls:((s=d.videoOptions)==null?void 0:s.controls)!=!1,autoplay:((v=d.videoOptions)==null?void 0:v.autoplay)!=!1,loop:((f=d.videoOptions)==null?void 0:f.loop)!=!1,muted:((m=d.videoOptions)==null?void 0:m.muted)==!0,initialTime:(Pe=(C=d.videoOptions)==null?void 0:C.initialTime)!=null?Pe:0,duration:(Se=(A=d.videoOptions)==null?void 0:A.duration)!=null?Se:0,showPoster:((P=d.videoOptions)==null?void 0:P.showPoster)!=!1,showProgress:((O=d.videoOptions)==null?void 0:O.showProgress)!=!1,showCenterPlayBtn:!1,enablePlayGesture:((S=d.videoOptions)==null?void 0:S.enablePlayGesture)==!0,showLoading:((q=d.videoOptions)==null?void 0:q.showLoading)!=!1,enableProgressGesture:((X=d.videoOptions)==null?void 0:X.enableProgressGesture)!=!1,objectFit:((R=d.videoOptions)==null?void 0:R.objectFit)||"contain",playBtnPosition:((z=d.videoOptions)==null?void 0:z.playBtnPosition)||"center",mobilenetHintType:(Te=(M=d.videoOptions)==null?void 0:M.mobilenetHintType)!=null?Te:1,autoPauseIfNavigate:((j=d.videoOptions)==null?void 0:j.autoPauseIfNavigate)!=!1,autoPauseIfOpenNative:((V=d.videoOptions)==null?void 0:V.autoPauseIfOpenNative)!=!1,vslideGesture:((J=d.videoOptions)==null?void 0:J.vslideGesture)!=!1,vslideGestureInFullscreen:((Q=d.videoOptions)==null?void 0:Q.vslideGestureInFullscreen)!=!1,codec:((H=d.videoOptions)==null?void 0:H.codec)||"hardware",httpCache:((N=d.videoOptions)==null?void 0:N.httpCache)!=!1,playStrategy:(Oe=(U=d.videoOptions)==null?void 0:U.playStrategy)!=null?Oe:2,header:((Z=d.videoOptions)==null?void 0:Z.header)||{},isLive:((K=d.videoOptions)==null?void 0:K.isLive)==!0,showRate:((u=d.videoOptions)==null?void 0:u.showRate)!=!1,rateList:((g=d.videoOptions)==null?void 0:g.rateList)||["0.5","0.8","1.0","1.25","1.5","2.0"],enableClick:((w=d.videoOptions)==null?void 0:w.enableClick)==!0,enableDblClick:((o=d.videoOptions)==null?void 0:o.enableDblClick)==!0,showWaitingTips:((k=d.videoOptions)==null?void 0:k.showWaitingTips)!=!1,waitingCount:((_=d.videoOptions)==null?void 0:_.waitingCount)||5,waitingMessage:((x=d.videoOptions)==null?void 0:x.waitingMessage)||"\u5F53\u524D\u7F51\u7EDC\u4E0D\u4F73",fillHeight:((ee=d.videoOptions)==null?void 0:ee.fillHeight)==!0,showFit:((re=d.videoOptions)==null?void 0:re.showFit)!=!1}),me=(0,t.getCurrentInstance)(),h=null,ye=(0,t.ref)(1),te=(0,t.ref)(0),be=(0,t.ref)(null),E=(0,t.ref)(!1),we=(0,t.computed)(()=>d.showPlayer!=!1),Ce=0,de=0,L=l;vt(()=>{(!h||h==null)&&setTimeout(()=>{F()},500)});let F=()=>{var y;let G=!h||h==null,I=d.video||{};return G&&I&&I.url&&((y=I.url)==null?void 0:y.length)>0&&(h=uni.createVideoContext("ml-player",me)),h},Fe=()=>{F(),E.value=!0,B.value=!1,L("play",h)},We=()=>{F(),E.value=!1,B.value=!1,L("pause",h)},ze=()=>{F(),te.value=0,B.value=!1,L("ended",h)},Me=()=>{F(),h&&(h==null||h.play(),E.value=!0,B.value=!1),L("playVideo",h)},je=y=>{E.value=!0,B.value=!1,L("timeupdate",h,y)};function Ve(y){L("fullscreenchange",h,y)}let Je=()=>{if(B.value=!0,p.value.showWaitingTips){if(te.value>=p.value.waitingCount)return ne(p.value.waitingMessage,"none"),te.value=0,!1;te.value=te.value+1}F(),L("waiting",h)},Qe=()=>{let y=["\u5305\u542B","\u586B\u5145","\u8986\u76D6"];uni.showActionSheet({title:"\u8BBE\u7F6E\u5C55\u793A\u5F62\u5F0F",alertText:"\u9009\u62E9\u5C55\u793A\u5F62\u5F0F",itemList:y,success:function(G){let I=G.tapIndex,ot=["contain","fill","cover"][I];p.value.objectFit=ot,ne("\u5C55\u793A\u5F62\u5F0F\uFF1A"+y[I],"none")}})},He=()=>{let y=p.value.rateList||["0.5","0.75","1.0","1.25","1.5","1.75","2.0","2.5"];y&&y.length>0&&uni.showActionSheet({title:"\u8BBE\u7F6E\u500D\u901F",alertText:"\u9009\u62E9\u500D\u901F",itemList:y,success:function(G){let I=Number(y[G.tapIndex]);h||F(),h&&!isNaN(I)&&(ye.value=I,h==null||h.playbackRate(I),ne("\u500D\u901F\uFF1A"+I,"none"))}})},Ze=y=>{ne("\u8D44\u6E90\u64AD\u653E\u9519\u8BEF","error"),E.value=!1,B.value=!1;let G={video:d.video,error:y};T("error","at components/ml-player/ml-player.vue:387","==========\u8D44\u6E90\u64AD\u653E\u9519\u8BEF========="),T("error","at components/ml-player/ml-player.vue:388",G),L("error",h,y)};function Ke(y){L("progress",h,y)}function Ye(y){L("loadedmetadata",h,y)}function Xe(y){L("fullscreenclick",h,y)}function $e(y){L("controlstoggle",h,y)}let et=()=>{p.value.enableClick==!0&&(F(),L("videoClick",h,d.video))},tt=()=>{let y=Date.now(),G=Ce;Ce=y,y-G<200?(clearTimeout(de),p.value.enableDblClick==!0&&L("doubleClick",h,d.video)):de=setTimeout(()=>{et()},200)},ne=(y,G)=>{uni.hideToast(),uni.showToast({title:y,icon:G||"none",mask:!1,duration:2e3})};function Ae(){h&&(h==null||h.pause(),h==null||h.stop()),clearTimeout(de),h=null,be.value=null,p.value=null,me=null}return Ie(()=>{h&&(h==null||h.pause())}),Re(()=>{Ae()}),(0,t.onUnmounted)(()=>{Ae()}),(y,G)=>((0,t.openBlock)(),(0,t.createElementBlock)(t.Fragment,null,[we.value&&e.video&&e.video.url?((0,t.openBlock)(),(0,t.createElementBlock)("u-video",{key:0,class:"ml-player",id:"ml-player",ref_key:"mlPlayer",ref:be,src:e.video.url,poster:e.video.poster,title:e.video.title,controls:p.value.controls,autoplay:p.value.autoplay,loop:p.value.loop,muted:p.value.muted,initialTime:p.value.initialTime,duration:p.value.duration,showFullscreenBtn:p.value.controls,showPlayBtn:p.value.controls,showCenterPlayBtn:p.value.showCenterPlayBtn,showLoading:p.value.showLoading,enableProgressGesture:p.value.enableProgressGesture,objectFit:p.value.objectFit,showMuteBtn:p.value.controls,playBtnPosition:p.value.playBtnPosition,autoPauseIfNavigate:p.value.autoPauseIfNavigate,autoPauseIfOpenNative:p.value.autoPauseIfOpenNative,vslideGesture:p.value.vslideGesture,vslideGestureInFullscreen:p.value.vslideGestureInFullscreen,codec:p.value.codec,httpCache:p.value.httpCache,playStrategy:p.value.playStrategy,showProgress:p.value.showProgress,pageGesture:p.value.vslideGesture,mobilenetHintType:p.value.mobilenetHintType,enablePlayGesture:p.value.enablePlayGesture,isLive:p.value.isLive,onClick:(0,t.withModifiers)(tt,["stop"]),onPlay:Fe,onPause:We,onEnded:ze,onTimeupdate:je,onFullscreenchange:Ve,onWaiting:Je,onError:Ze,onProgress:Ke,onLoadedmetadata:Ye,onFullscreenclick:Xe,onControlstoggle:$e,webkitPlaysinline:"true",playsinline:"true",xWebkitAirplay:"allow",x5VideoPlayerType:"h5-page",x5VideoOrientation:"portrait",style:(0,t.normalizeStyle)({width:ae.value+"px",height:Y.value+"px"})},[(0,t.createElementVNode)("u-scalable",{style:{position:"absolute",left:"0",right:"0",top:"0",bottom:"0"}},[p.value.showRate||p.value.showFit?((0,t.openBlock)(),(0,t.createElementBlock)("cover-view",{key:0,class:"rateIcon",style:(0,t.normalizeStyle)({top:Y.value/2+"px"})},[p.value.showRate?((0,t.openBlock)(),(0,t.createElementBlock)("view",{key:0,onClick:(0,t.withModifiers)(He,["stop"])},[(0,t.createElementVNode)("u-text",{class:"rateText"},(0,t.toDisplayString)(ye.value)+"\xD7",1)])):(0,t.createCommentVNode)("",!0),p.value.showFit?((0,t.openBlock)(),(0,t.createElementBlock)("view",{key:1,style:{"margin-top":"2px"},onClick:(0,t.withModifiers)(Qe,["stop"])},[(0,t.createElementVNode)("u-text",{class:"rateText",style:{"font-size":"17px","line-height":"17px"}},"\u2699")])):(0,t.createCommentVNode)("",!0)],4)):(0,t.createCommentVNode)("",!0),(0,t.createElementVNode)("cover-view",null,[(0,t.renderSlot)(y.$slots,"default")]),!E.value||B.value?((0,t.openBlock)(),(0,t.createElementBlock)("view",{key:1,class:"center-play-mask",style:(0,t.normalizeStyle)({width:ae.value+"px",height:fe.value+"px"})},[!B.value&&!E.value?((0,t.openBlock)(),(0,t.createElementBlock)("cover-image",{key:0,src:"data:image/png;base64,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",class:"center-play-btn",onClick:(0,t.withModifiers)(Me,["stop"])})):B.value?((0,t.openBlock)(),(0,t.createElementBlock)("cover-image",{key:1,src:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAAAXNSR0IArs4c6QAABX9JREFUaEPtWVuME2UU/s5MFwFFCRh4gMJ2irLMbNspm0B0o/IgoIaoGORBMQZFY6IRlPhiRMVLiAleow9GhA1GgkYMvmi8BWIIKAE63e502Ww7Xa4KSiSCLnbbOTKlXdo67XR70Uqcx3/O+c73/ef/z38j1Pj5+8NTRdG1mBmnNK+ypRhuXiIx+rT5+zoQ3QtgCMAmTVKeqTHssDvVAjQ71tthCua3AK7K4miDSfHGvra2Mzlc1dA/ALAsPw4Db4UlZWUtsXO+NQlQDX3gfI9OLyTCGzSp/SGrzRfv9YlkdtsR5RbX1LB75rFaRVQtoKM/4k2LQsyGgKFJitdqV+P6PSB8aEeSmOaHvPI31r+ZBw+OG9uSWsSC4AbSezWPb2elwqoXcHzf2PS50b8ANKYwGH2lSfJCq609FnG7BOGwDZmzoKRb8wRPB/p1hQT+EkRTcnYMbAtLypJKRDgKyPbiUgZiLoFe3d8q/5gDDhrR+xi8OS/QbybEOd1SW9/wHEhEV4H59QIyhOWaR+my2gKG3ktAWzFZBq0JS/JLTiLKCggY+lICPsqbMJGQpPjzQYMD+q1gWmFVIVMQ1nW3tiWKg6oJ/Q4wloGQBFOXJslfWzZ+Q79BAL4rQXJ4KJYTUVaAakTDABcQBmhBjoBT7zj9D8ajNzNxRozNd1yTlOFhVQrLQYB+AEAw3zl/8jkRdPo/t7//yj/FpFWJrvibLaNL8yrLnTCcBNwN4ONyQ8gpgNP/wEB0MZn8aZHdobSYDkam+3918necxAFDX0bAErtJ7ARe6f/sgrgWwGxi2vzHkPCytRh29PVdnXalFkKgKTDRo3nlz4sxHQVUSqLedrMT+i0mY2veKg8CIkMw5/dIvhN5o6LeoWvHU47oE1qG0A9ggg3aJ5qkWEM78w1n4EJJo7fB5kSQ8LwmyRtqp1IdQtDQVzOwvpR3yjSn9czwHSkQULyvyTeqjkb1XqoRfRvgR0uXTropJMmZ9SOTATXW0wmBdhU4ED2heeQ3qqdRvWe2cFi7WNsvOYhxUUU5ezEDzIKaiFobM0/WwySX6A5NaztePY3qPeft2OE63TpJA0MpRmHGC2Gv8lzpOQCeDPAaTWp/r3oKtXv6DnVLYlq09lmdF9HoHU2SH8tHb9oymiPpT+hzBZM6qEXYbjciml5ATohqROeDuZNAJ1KgXRHvrEhBFao96Y1DUI1oF8D3F0V4SpOU9U2fgUBcX0uEZ227h3BnRoA/Fp4ExqjuawJHG9ePI0duNyKTXRB+KunJfIwKdoN5J6WRh6u/R4mdauFypRq6dYJqzbaeTA7CHVWUZP3pjBwxU4EY35fxNElN6DEwMrcIAH4eT2On7fR4zo08XGM81LiugyDboTPzVgrEI3cRCdssA2Y8EPYqmxpDpTrUzK2FiB8AXF6EcDgtptWmr0IW6ayIjQDmZEQwtqfIfMQ6F/wnBJTL3f8CqhvZ9fO6tDJgXSMS6GTu0rV+/dQ4pOEMBAz9TQIet0IRaEVIkt9vXNj6IecJ6NlHoI6MAMLGkEd5sH5hGoeUL+BhAr2bCWWandoM3+7Gha0fcsEktg73gsBnDkg+21eV+oWtHGnO0d6JQylcG2qdtcfO69KqQpX3S/NYXvoZsE5FLRAXjW4Zt2WP2z3YPH1/gUnZDMi6PuqyMYgx4D7/ZldwqdosQhyHkGro1iPDeDC+0LzKbY0iblWbvVNnnRopvqOAgBFZQCxcLwrprfs9/oMjDVCpvRrveQUC9eZeLyv1cxRQKdC/ZVezgOAhXTbTeJLN1NPdMwIn/2khNQtQDf0zALcz6MWwJNteQAWO9E2hodRrZjq1ut53T7ULyDxi0yoB6ZWltiDBgd7r2DR3N2KP9RdxLfhyI6cVtQAAAABJRU5ErkJggg==",class:"center-loading"})):(0,t.createCommentVNode)("",!0),!E.value||B.value?((0,t.openBlock)(),(0,t.createElementBlock)("u-text",{key:2,style:{color:"#fff","text-align":"center"}},"\u70B9\u51FB\u64AD\u653E")):(0,t.createCommentVNode)("",!0)],4)):(0,t.createCommentVNode)("",!0)])],44,["src","poster","title","controls","autoplay","loop","muted","initialTime","duration","showFullscreenBtn","showPlayBtn","showCenterPlayBtn","showLoading","enableProgressGesture","objectFit","showMuteBtn","playBtnPosition","autoPauseIfNavigate","autoPauseIfOpenNative","vslideGesture","vslideGestureInFullscreen","codec","httpCache","playStrategy","showProgress","pageGesture","mobilenetHintType","enablePlayGesture","isLive"])):(0,t.createCommentVNode)("",!0),!we.value&&p.value.showPoster&&e.video.poster?((0,t.openBlock)(),(0,t.createElementBlock)("u-image",{key:1,src:e.video.poster,style:(0,t.normalizeStyle)({width:ae.value+"px",height:Y.value+"px"})},null,12,["src"])):(0,t.createCommentVNode)("",!0)],64))}}),yt=le(mt,[["styles",[ft]]]),bt={"ml-swiper-item-view-box":{"":{position:"relative",backgroundColor:"rgba(0,0,0,0.7)"}},"ml-swiper-item-view-right-box":{"":{position:"absolute",bottom:100,paddingTop:5,paddingRight:5,paddingBottom:5,paddingLeft:5,right:1,flexWrap:"wrap",flexDirection:"column"}},"ml-swiper-item-view-bottom-box":{"":{position:"absolute",bottom:1,left:0,flexWrap:"wrap",flexDirection:"column"}},"center-play-mask":{"":{position:"fixed",top:0,left:0,alignItems:"center",justifyContent:"center"}},"center-play-btn":{".center-play-mask ":{width:"140rpx",height:"140rpx"}},"center-loading":{".center-play-mask ":{width:"100rpx",height:"100rpx"}},"ml-swiper-view":{"":{backgroundColor:"rgba(0,0,0,0.7)"}},"ml-swiper":{"":{backgroundColor:"rgba(0,0,0,0.7)"}},"ml-swiper-item":{"":{backgroundColor:"rgba(0,0,0,0.7)"}},"ml-swiper-item-video":{"":{backgroundColor:"rgba(0,0,0,0.7)"}}},wt=Object.assign({name:"ml-swiper"},{__name:"ml-swiper",props:{width:{type:Number,default:0,required:!1},height:{type:Number,default:0,required:!1},rightStyle:{type:[Object,String],default:{},required:!1},bottomStyle:{type:[Object,String],default:{},required:!1},videoList:{type:Array,default:[],required:!0},count:{type:Number,default:2,required:!1},showPlay:{type:Boolean,default:!0,required:!1},hidePause:{type:Boolean,default:!0,required:!1}},emits:["change","play","pause","ended","error","waiting","videoClick","doubleClick","loadMore","maskClick"],setup(e,{emit:l}){let r=uni.getSystemInfoSync(),c=e,b=(0,t.ref)({width:c.width||r.windowWidth,height:c.height||r.windowHeight,controls:!1,initialTime:0,vslideGesture:!1,showRate:!1,showFit:!1,enableClick:!0,enableDblClick:!0,autoplay:!0,showPoster:!0,loop:!0,muted:!1,objectFit:"contain"}),s=null,v=(0,t.ref)(0),f=(0,t.computed)(()=>c.videoList||[]),m=(0,t.ref)(!1),C=(0,t.ref)(!1),A=(0,t.ref)(!0),P=(0,t.computed)(()=>({width:b.value.width+"px",height:b.value.height+"px"})),O=l,S=u=>{if(m.value)return;m.value=!0,setTimeout(()=>{m.value=!1},500);let g=u.detail.current;U(),v.value=g,q()},q=()=>{X();let u=f.value.length;O("change",v.value,u)},X=()=>{let u=f.value.length,g=Number(isNaN(c.count)?2:c.count);Number(Number(u)-g)==v.value&&O("loadMore",v.value,u)},R=0,z=u=>{if(Date.now()-R<200){N(s,f.value[v.value]),R=0;return}R=Date.now()},M=u=>{u&&(s=u),C.value=!0,A.value=!1,O("play",s)},j=u=>{u&&(s=u),C.value=!1,O("pause",s)},V=u=>{u&&(s=u),A.value=!0,O("waiting",s)},J=u=>{u&&(s=u),O("ended",s)},Q=(u,g)=>{u&&(s=u),A.value=!1,O("error",s,g)},H=(u,g)=>{u&&(s=u),C.value===!0?U():Z(),O("videoClick",s,g)},N=(u,g)=>{u&&(s=u),O("doubleClick",s,g)};function U(){if(s)try{s==null||s.pause(),C.value=!1}catch(u){T("error","at components/ml-swiper/ml-swiper.vue:366",u)}}function Z(){if(s){T("log","at components/ml-swiper/ml-swiper.vue:373",s);try{s==null||s.play(),C.value=!0,A.value=!1}catch(u){T("error","at components/ml-swiper/ml-swiper.vue:379",u)}}}function K(){if(s)try{s==null||s.stop()}catch(u){T("error","at components/ml-swiper/ml-swiper.vue:389",u)}}return he(()=>{c.showPlay&&Z()}),(0,t.onMounted)(()=>{}),Ie(()=>{c.hidePause&&U()}),Re(()=>{R=0,K(),v.value=0,m.value=!1,C.value=!1,A.value=!0,s=null}),(u,g)=>{let w=ge((0,t.resolveDynamicComponent)("ml-player"),yt),o=(0,t.resolveComponent)("swiper-item"),k=(0,t.resolveComponent)("swiper");return(0,t.openBlock)(),(0,t.createElementBlock)("view",{class:"ml-swiper-view",renderWhole:!0},[f.value&&f.value.length>0?((0,t.openBlock)(),(0,t.createBlock)(k,{key:0,class:"ml-swiper",current:v.value,circular:!1,vertical:!0,"skip-hidden-item-layout":!0,style:(0,t.normalizeStyle)(P.value),onTouchend:z,onChange:S},{default:(0,t.withCtx)(()=>[((0,t.openBlock)(!0),(0,t.createElementBlock)(t.Fragment,null,(0,t.renderList)(f.value,(_,x)=>((0,t.openBlock)(),(0,t.createBlock)(o,{class:"ml-swiper-item",key:x},{default:(0,t.withCtx)(()=>[(0,t.createElementVNode)("cover-view",{class:"ml-swiper-item-view-box",style:(0,t.normalizeStyle)(P.value)},[(0,t.createVNode)(w,{showPlayer:v.value===x,video:_,videoOptions:b.value,onVideoClick:H,onDoubleClick:N,onPlay:M,onPause:j,onWaiting:V,onEnded:J,onError:Q},null,8,["showPlayer","video","videoOptions"]),(0,t.createElementVNode)("cover-view",{class:"ml-swiper-item-view-right-box",style:(0,t.normalizeStyle)(e.rightStyle)},[(0,t.renderSlot)(u.$slots,"right",{video:_,index:x})],4),(0,t.createElementVNode)("cover-view",{class:"ml-swiper-item-view-bottom-box",style:(0,t.normalizeStyle)(e.bottomStyle)},[(0,t.renderSlot)(u.$slots,"bottom",{video:_,index:x})],4)],4)]),_:2},1024))),128))]),_:3},8,["current","style"])):(0,t.createCommentVNode)("",!0)])}}}),qe=le(wt,[["styles",[bt]]]),Ct=(e,l,r)=>new Promise((c,b)=>{let s=!1,{method:v="POST",header:f,isLoad:m=!0}=r;m&&(uni.showLoading({title:"\u52A0\u8F7D\u4E2D...",mask:!0}),s=!0);let C=e;uni.request({url:C,method:v,data:l,header:f,success:A=>{let{data:P}=A;switch(s&&uni.hideLoading(),P.code){case 0:c(P);break;default:uni.showToast({title:P.msg||"\u7F51\u7EDC\u51FA\u9519",icon:"none"}),P.data.reload&&(uni.clearStorageSync(),uni.reLaunch({url:"/pagesSub/pages/login/login"})),b(P.msg||"\u7F51\u7EDC\u51FA\u9519");break}},complete:A=>{s&&uni.hideLoading(),T("log","at apis/https.js:50",C,l,A)}})}),At=(e,l,r)=>new Promise((c,b)=>{let{method:s="POST",header:v,isLoad:f=!0}=r,m=!1;f&&(uni.showLoading({title:"\u52A0\u8F7D\u4E2D...",mask:!0}),m=!0);let C=e;uni.request({url:C,method:s,data:l,header:v,success:A=>{let{data:P}=A;switch(m&&uni.hideLoading(),P.ErrCode){case"0":c(P);break;default:uni.showToast({title:P.ErrMsg||"\u7F51\u7EDC\u51FA\u9519",icon:"none"}),c(P);break}},complete:A=>{m&&uni.hideLoading(),T("log","at apis/httpsNew.js:50",C,l,A)}})}),oe=(0,Ge.defineStore)("userInfo",()=>{let e=(0,t.ref)(""),l=(0,t.ref)({}),r=(0,t.ref)(0),c=(0,t.ref)(""),b=(0,t.ref)(0),s=(0,t.ref)(""),v=(0,t.ref)(""),f=(0,t.ref)(0),m=(0,t.ref)(0);function C(O){let S=O.user.roles[0];l.value=O.user,e.value=O.token,S!=null&&(r.value=S.teamID,c.value=S.teamName,b.value=S.companyID,s.value=S.companyName,v.value=S.roleName,f.value=S.role_ID,m.value=S.beanRate)}function A(O,S){switch(O){case"token":e.value=S;break;case"user":l.value=S;break;case"teamID":r.value=S;break;case"teamName":c.value=S;break;case"companyID":b.value=S;break;case"companyName":s.value=S;break;case"roleName":v.value=S;break;case"roleID":f.value=S;break;case"beanRate":m.value=S;break}}function P(){e.value="",l.value={},r.value=0,c.value="",b.value=0,s.value="",v.value="",f.value=0,m.value=0}return{token:e,user:l,teamID:r,teamName:c,companyID:b,companyName:s,roleName:v,roleID:f,beanRate:m,setInfo:C,setItem:A,clear:P}},{unistorage:!0}),Pt=(e,l,r)=>new Promise((c,b)=>{let{method:s="POST",header:v,isLoad:f=!0}=r,m=!1;f&&(uni.showLoading({title:"\u52A0\u8F7D\u4E2D...",mask:!0}),m=!0);let C=e;uni.request({url:C,method:s,data:l,header:v,success:A=>{let{data:P}=A;switch(m&&uni.hideLoading(),P.ErrCode){case"0":c(P);break;default:c(P);break}},complete:A=>{m&&uni.hideLoading(),T("log","at apis/httpsNewWithoutNotion.js:50",C,l,A)}})}),W={AppUser:{Login:e=>n("appUser/login",e),Register:e=>n("appUser/register",e),CheckExist:e=>n("appUser/checkExist",e,"GET"),UserList:e=>n("appUser/getUserList",e,"GET"),CheckUserStatus:e=>i("Clothing/User/CheckUserStatus",e,"POST"),AddLoginLog:e=>i("Clothing/Staff/AddLoginLog",e,"POST"),ModifyPwd:e=>i("Clothing/Staff/ModifyPwd",e,"POST"),AddMember:e=>i("Clothing/User/AddMember",e,"POST"),CheckUserAuth:e=>i("Clothing/User/CheckUserAuth",e,"POST"),CheckCheckMemberStatus:e=>i("Clothing/User/CheckCheckMemberStatus",e,"POST")},AppRole:{List:()=>n("appRole/getAppRoleList",null,"GET")},Company:{List:()=>n("company/getCompanyList",null,"GET"),Join:e=>n("company/joinCompany",e),Check:e=>n("companyApply/optApply",e,"PUT"),Apply:e=>n("companyApply/findCompanyApply",e,"GET"),DelUser:e=>n("company/deleteStaff",e,"DELETE")},Team:{List:e=>n("team/getTeamList",e,"GET"),Join:e=>n("team/joinTeam",e),ApplyDetail:e=>n("teamApply/findTeamApply",e,"GET"),Check:e=>n("teamApply/optApply",e,"PUT"),DelUser:e=>n("team/deleteMember",e,"DELETE")},Staff:{GroupList:e=>i("Clothing/Staff/GroupList",e,"POST"),UserList:e=>i("Clothing/Staff/UserList",e,"POST"),RoleList:()=>i("Clothing/Staff/RoleList",null,"POST"),AddGroup:e=>i("Clothing/Staff/AddGroup",e,"POST"),AddTeam:e=>i("Clothing/Staff/AddTeam",e,"POST"),EditTeam:e=>i("Clothing/Staff/EditTeam",e,"POST"),DeleteTeam:e=>i("Clothing/Staff/DeleteTeam",e,"POST"),UpdateGroup:e=>i("Clothing/Staff/UpdateGroup",e,"POST"),DeleteGroup:e=>i("Clothing/Staff/DeleteGroup",e,"POST"),ExistCompany:e=>i("Clothing/Staff/ExistCompany",e,"POST"),AddCompany:e=>i("Clothing/Staff/AddCompany",e,"POST"),UserRoleList:e=>i("Clothing/Staff/UserRoleList",e,"POST"),UserListByComp:e=>D("Clothing/Staff/UserListByComp",e,"POST")},CroppingRecord:{List:e=>n("croppingRecord/getCroppingRecordList",e,"GET"),Detail:e=>n("croppingRecord/findCroppingRecord",e,"GET"),Update:e=>n("croppingRecord/updateCroppingRecord",e,"PUT"),Add:e=>n("croppingRecord/createCroppingRecord",e),AddNew:e=>i("Clothing/CroppingRecord/AddCroppingRecord",e,"POST"),UpdatePrintStatus:e=>i("Clothing/CroppingRecord/UpdatePrintStatus",e,"POST"),GetPrintStatus:e=>i("Clothing/CroppingRecord/GetPrintStatus",e,"POST")},Style:{List:e=>n("style/getStyleList",e,"GET"),Add:e=>n("style/createStyle",e),Detail:e=>n("style/findStyle",e,"GET"),Update:e=>n("style/updateStyle",e,"PUT"),DeleteStyle:e=>i("Clothing/Process/DeleteStyle",e,"POST"),StyleList:e=>i("Clothing/CroppingRecord/StyleList",e,"POST")},Cloth:{List:e=>n("cloth/getClothList",e,"GET"),Add:e=>n("cloth/creatCloth",e),Detail:e=>n("cloth/findCloth",e,"GET"),Update:e=>n("cloth/updateCloth",e,"PUT"),AddCloth:e=>i("Clothing/Cloth/AddCloth",e,"POST"),UpdateCloth:e=>i("Clothing/Cloth/UpdateCloth",e,"POST"),ClothListByPage:e=>i("Clothing/Cloth/ClothListByPage",e,"POST")},Wallet:{List:e=>n("userWallet/getUserWalletList",e,"GET"),My:()=>n("userWallet/getMyWalletList",null,"GET"),QueryWallet:e=>i("Clothing/UserWallet/QueryWallet",e,"POST"),ExportWallet:e=>i("Clothing/UserWallet/Export2",e,"POST"),WageSettle:e=>i("Clothing/UserWallet/WageSettle",e,"POST"),WageSettleCancel:e=>i("Clothing/UserWallet/WageSettleCancel",e,"POST")},Job:{List:e=>n("jobQuestion/getJobQuestionList",e,"GET"),Detail:e=>n("job/findJob",e,"GET"),Question:e=>n("jobQuestion/findJobQuestion",e,"GET"),Update:e=>n("jobQuestion/handleJobQuestion",e,"PUT"),Add:e=>n("jobQuestion/createJobQuestion",e),Job:e=>n("job/getJobList",e,"GET"),Process:e=>n("job/getJobGroupByProcess",e,"GET"),ToApply:e=>n("job/jobAuditApply",e,"PUT"),Check:e=>n("job/jobAuditOpt",e,"PUT"),ChangeWorker:e=>n("job/changeWorker",e,"PUT"),Task:e=>n("job/postJobList",e),Wages:e=>n("job/getWagesDetail",e,"GET"),Apply:{Detail:e=>n("jobApply/findJobApply",e,"GET"),Add:e=>n("jobApply/createJobApply",e),Check:e=>n("jobApply/optApply",e,"PUT")},JobReceiveList:e=>i("Clothing/Job/JobReceiveList",e,"POST"),JobReceiveDetail:e=>i("Clothing/Job/JobReceiveDetail",e,"POST"),UpdateJobStatus:e=>i("Clothing/Job/UpdateJobStatus",e,"POST"),DeleteJob:e=>D("Clothing/Job/DeleteJob",e,"POST"),AddJob:e=>i("Clothing/Job/AddJob",e,"POST"),AddJobForOther:e=>i("Clothing/Job/AddJobForOther",e,"POST"),AddJobAll:e=>i("Clothing/Job/AddJobAll",e,"POST"),JobFinishList:e=>D("Clothing/Job/JobFinishList",e,"POST"),JobReceiveListByCroppingRecord:e=>D("Clothing/Job/JobReceiveListByCroppingRecord",e,"POST")},Inventory:{Stock:e=>n("inventory/getInventoryList",e,"GET")},Process:{List:e=>n("process/getProcessList",e,"GET"),ListNew:e=>i("Clothing/Process/ProcessListNew",e,"POST"),Detail:e=>n("process/findProcess",e,"GET"),Add:e=>n("process/createProcess",e),Update:e=>n("process/updateProcess",e,"PUT"),UpdateProcess:e=>i("Clothing/Process/UpdateProcess",e,"POST"),AddProcess:e=>i("Clothing/Process/AddProcess",e,"POST"),ProcessList:e=>i("Clothing/Process/ProcessList",e,"POST"),ProcessListByPage:e=>i("Clothing/Process/ProcessListByPage",e,"POST"),GetSizeQuanlity:e=>i("Clothing/Process/GetSizeQuanlity",e,"POST"),GetList:e=>i("Clothing/Process/StyleList",e,"POST"),GetStandard:e=>i("Clothing/Process/GetStandard",e,"POST"),AddStandard:e=>D("Clothing/Process/AddStandard",e,"POST"),GetMyStandard:e=>D("Clothing/Process/GetMyStandard",e,"POST"),StyleSettle:e=>i("Clothing/Process/StyleSettle",e,"POST"),StyleSettleCancel:e=>i("Clothing/Process/StyleSettleCancel",e,"POST")},Chat:{GroupList:e=>i("IM/Group/GroupList",e,"POST"),AddGroup:e=>D("IM/Group/AddGroup",e,"POST"),CreateGroup:e=>i("IM/Group/CreateGroup",e,"POST"),GetUser:e=>i("IM/Chat/GetUser",e,"POST")},Message:{List:e=>n("msgBox/getMyMsgBoxList",e,"GET"),Send:e=>n("msgBox/getMySendMsgList",e,"GET"),Read:e=>n("msgBox/setRead",e,"GET")},Banner:{List:()=>n("banner/getBannerList",null,"GET"),Find:e=>n("banner/findBanner",e,"GET")},Computation:{Do:e=>n("computation/doComputation",e)},Order:{List:e=>n("order/getOrderList",e,"GET"),Detail:e=>n("order/findOrder",e,"GET"),Goods:()=>n("rechargeOption/getRechargeOptionList",null,"GET"),Add:e=>n("order/createOrder",e),Pay:e=>n("order/payOrder",e)},Common:{Request:(e,l,r)=>n(e,l,r),Request1:(e,l,r)=>i(e,l,r),GetInfo:e=>i("Clothing/Common/GetInfo",e,"POST"),AddColors:e=>i("Clothing/Common/AddColors",e,"POST"),GetColors:e=>i("Clothing/Common/GetColors",e,"POST")},Question:{QuestionList:e=>i("Clothing/Job/QuestionList",e,"POST"),ReplyQuestion:e=>i("Clothing/Job/ReplyQuestion",e,"POST")},Adv:{AdvList:e=>D("ADV/AdvInfo/AdvList",e,"POST"),AddAdv:e=>D("ADV/AdvInfo/AddAdv",e,"POST"),DeleteAdv:e=>i("ADV/AdvInfo/DeleteAdv",e,"POST"),PayAdv:e=>i("ADV/AdvInfo/PayAdv",e,"POST"),BigList:e=>D("ADV/AdvInfo/BigList",e,"POST"),SmallList:e=>D("ADV/AdvInfo/SmallList",e,"POST"),BuyList:e=>i("ADV/AdvInfo/BuyList",e,"POST"),AdvDetial:e=>i("ADV/AdvInfo/AdvDetail",e,"POST")},Url:{baseUrl:"http://8.138.8.6:8889",baseUrlBiz:"http://8.138.8.6:8890",updateUrl:"https://wxapi.ruiruicaikuai.com/",baseUrlFileUpload:"https://wxapi.ruiruicaikuai.com/api/Files/Upload"},App:{vuex_version:"1.3.7",agent_code:""}},St="http://8.138.8.6:8889",Ne="http://8.138.8.6:8890",n=(e,l,r="POST")=>{let{token:c}=oe(),b=St+"/api/"+e;return Ct(b,l,{header:{"x-token":c},method:r})},i=(e,l,r="POST")=>{oe();let c=Ne+"/api/"+e;return At(c,l,{header:{},method:r})},D=(e,l,r="POST")=>{oe();let c=Ne+"/api/"+e;return Pt(c,l,{header:{},method:r})};var a=ie(ce()),Ee=ie(pe());var Tt={imgV:{"":{width:100,textAlign:"center",minHeight:65}},work:{"":{backgroundColor:"#ffffff",marginTop:"10rpx",marginRight:"10rpx",marginBottom:"10rpx",marginLeft:"10rpx",borderRadius:6}},title:{".work ":{display:"flex",alignItems:"center",height:"80rpx",paddingTop:0,paddingRight:"20rpx",paddingBottom:0,paddingLeft:"20rpx",borderBottomWidth:1,borderBottomStyle:"solid",borderBottomColor:"#e5e7eb"}},menu:{".work ":{gridTemplateColumns:"repeat(3, minmax(0, 1fr))",gap:"30rpx",paddingTop:"30rpx",paddingRight:0,paddingBottom:"30rpx",paddingLeft:0,fontSize:"24rpx",textAlign:"center"}},item:{".work .menu ":{color:"#999999"},".work .menu_1 ":{color:"#999999",textAlign:"center"},".search ":{display:"flex",alignItems:"center",gap:"20rpx",fontSize:"28rpx"}},img:{".work .menu .item ":{height:"128rpx",width:"128rpx",borderRadius:6,marginTop:0,marginBottom:"10rpx"},".work .menu_1 .item ":{height:"100rpx",width:"100rpx",borderRadius:6,marginTop:"10rpx",marginRight:"10rpx",marginBottom:"20rpx",marginLeft:"10rpx"},".top_nav .top_content ":{width:"44rpx",height:"44rpx"}},menu_1:{".work ":{gridTemplateColumns:"repeat(4, minmax(0, 1fr))",gap:"30rpx",paddingTop:"30rpx",paddingRight:0,paddingBottom:"30rpx",paddingLeft:0,fontSize:"24rpx",textAlign:"center"}},search:{"":{gridTemplateColumns:"repeat(1, minmax(0, 1fr))",paddingTop:"20rpx",paddingRight:"20rpx",paddingBottom:"20rpx",paddingLeft:"20rpx",gap:"20rpx",backgroundColor:"#ffffff"},".top_nav .top_content ":{position:"absolute",right:20,top:"35rpx"}},label:{".search .item ":{flexShrink:0}},input:{".search .item ":{flex:1,backgroundColor:"#f8f8f8",borderRadius:"10rpx",paddingTop:0,paddingRight:"20rpx",paddingBottom:0,paddingLeft:"20rpx",minHeight:"60rpx"}},value:{".search .item .input ":{display:"flex",alignItems:"center",justifyContent:"space-between",minHeight:"60rpx","fontFamily::after":'"iconfont"',"content::after":'"\\e840"',"color::after":"#999999","content:empty::before":'"\u8BF7\u9009\u62E9"',"color:empty::before":"#808080"}},submit:{".search ":{backgroundColor:"#007aff",color:"#ffffff"}},btnGroup:{".search ":{"!width":"100rpx"}},submit_deploy:{"":{position:"fixed",left:"20rpx",right:"20rpx",backgroundColor:"#007aff",color:"#ffffff",height:"80rpx",bottom:"10rpx"}},"person-head":{"":{position:"relative",backgroundColor:"#ffffff",marginLeft:"20rpx",marginRight:"20rpx"}},videoTitle:{"":{paddingTop:5,paddingRight:5,paddingBottom:5,paddingLeft:5,color:"#de4a00",fontSize:13,lines:13,whiteSpace:"normal"}},tabsviewContent:{"":{position:"fixed","!minWidth":"750rpx"}},tabsview:{"":{"!minWidth":"750rpx"}},top_nav:{"":{position:"fixed",top:0,left:0,right:0,backgroundImage:"linear-gradient(to bottom, rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0))"}},top_content:{".top_nav ":{paddingTop:"30rpx",flexDirection:"row",alignItems:"center",justifyContent:"center"}},player:{".top_nav .top_content ":{position:"absolute",left:20,top:"35rpx"}},content_btn:{".top_nav .top_content ":{flexDirection:"row",width:220,alignItems:"center",justifyContent:"space-around"}},content_item:{".top_nav .top_content .content_btn ":{position:"relative",height:30}},line_on:{".top_nav .top_content .content_btn .content_item ":{position:"absolute",width:50,height:2,backgroundColor:"#FFFFFF",bottom:0,left:2,borderRadius:"4rpx"}},item_title:{".top_nav .top_content .content_btn .content_item ":{color:"#dcdcdc",fontSize:"36rpx",fontWeight:"bold"}},i_on:{".top_nav .top_content .content_btn .content_item ":{fontWeight:"bold",fontSize:"38rpx","!color":"#FFFFFF"}}},Ot={__name:"buyList",setup(e){let l=oe(),{user:r,roleID:c,teamID:b}=(0,Ee.storeToRefs)(l),s=(0,a.ref)(0),v=(0,a.ref)([]),f=(0,a.ref)([]);(0,a.ref)([{type:"video",topTip:"\u8FD9\u662F\u4E00\u4E2A\u5C0F\u5587\u53ED----\u5E95\u90E8\u63D0\u793A",poster:"https://img1.baidu.com/it/u=1314619043,374926406&fm=253&fmt=auto?w=348&h=500",src:"http://www.w3school.com.cn/example/html5/mov_bbb.mp4",bottomTip:""},{type:"image",topTip:"\u8FD9\u662F\u4E00\u4E2A\u5C0F\u5587\u53ED----\u5E95\u90E8\u63D0\u793A",src:"https://img2.baidu.com/it/u=3256616248,1972425356&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=1039"},{type:"image",topTip:"\u8FD9\u662F\u4E00\u4E2A\u5C0F\u5587\u53ED----\u5E95\u90E8\u63D0\u793A",src:"https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fb-ssl.duitang.com%2Fuploads%2Fitem%2F201707%2F23%2F20170723111737_JZGmC.thumb.700_0.jpeg&refer=http%3A%2F%2Fb-ssl.duitang.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1725513912&t=01acc1e9efcbfacd570fed69edd5a9aa"},{type:"video",topTip:"\u8FD9\u662F\u4E00\u4E2A\u5C0F\u5587\u53ED----\u5E95\u90E8\u63D0\u793A",currentTime:120,poster:"https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fss2.meipian.me%2Fusers%2F3107464%2F269b3689a58b4e1f8eb7a882eae338fb.jpg%3Fmeipian-raw%2Fbucket%2Fivwen%2Fkey%2FdXNlcnMvMzEwNzQ2NC8yNjliMzY4OWE1OGI0ZTFmOGViN2E4ODJlYWUzMzhmYi5qcGc%3D%2Fsign%2F203e153600702e8c1f73d97470fd9234.jpg&refer=http%3A%2F%2Fss2.meipian.me&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1725513912&t=158a229b97ec18cbcc9a07aabc00e685",src:"https://www.w3schools.com/html/movie.mp4"}]),(0,a.ref)([]),(0,a.ref)(""),(0,a.ref)([]);let m=uni.getSystemInfoSync(),C=m.windowWidth,A=m.windowHeight,P={top:"36px"},O=(0,a.ref)([]),S=(0,a.ref)(0),q=(0,a.ref)(0),X=0,R=(g,w)=>{T("log","at pagesSub/pages/adv/buyList.nvue:195","onchange-\u5F53\u524D\u7D22\u5F15:",g+"aa"+w),S.value=g,g==0&&uni.showToast({title:"\u5F53\u524D\u5DF2\u662F\u7B2C\u4E00\u4E2A\u89C6\u9891",icon:"none",mask:!1}),g==w-1&&uni.showToast({title:"\u5F53\u524D\u5DF2\u662F\u6700\u540E\u4E00\u4E2A\u89C6\u9891",icon:"none",mask:!1})},z=(g,w)=>{T("log","at pagesSub/pages/adv/buyList.nvue:207","\u52A0\u8F7D\u66F4\u6240\u89C6\u9891\uFF1A",g+" / "+w),!(q.value>5)&&(Z().forEach(o=>{o.title=O.value.length+"\uFF0C"+o.title+o.title+o.title,O.value.push(o)}),q.value=q.value+1)},M=g=>{T("log","at pagesSub/pages/adv/buyList.nvue:221","\u89C6\u9891\u5F00\u59CB\u64AD\u653E")},j=g=>{T("log","at pagesSub/pages/adv/buyList.nvue:228","\u89C6\u9891\u6682\u505C\u64AD\u653E")},V=g=>{T("log","at pagesSub/pages/adv/buyList.nvue:235","\u89C6\u9891\u64AD\u653E\u7ED3\u675F")},J=(g,w)=>{T("error","at pagesSub/pages/adv/buyList.nvue:242","\u89C6\u9891\u64AD\u653E\u51FA\u9519\uFF1A",w)},Q=g=>{T("error","at pagesSub/pages/adv/buyList.nvue:249","\u89C6\u9891\u51FA\u73B0\u7F13\u51B2")},H=(g,w)=>{T("log","at pagesSub/pages/adv/buyList.nvue:256","\u70B9\u51FB\u4E86\u89C6\u9891\uFF1A",w)},N=(g,w)=>{phone.value=w.author.tel,T("log","at pagesSub/pages/adv/buyList.nvue:264","\u53CC\u51FB\u4E86\u89C6\u9891\uFF1A",w),uni.showModal({title:"\u63D0\u793A",content:"\u62E8\u6253\u7535\u8BDD\u7ED9\u53D1\u5E03\u4EBA\uFF1F",confirmText:"\u62E8\u6253",cancelText:"\u53D6\u6D88",success:function(o){o.confirm?uni.makePhoneCall({phoneNumber:phone.value}):o.cancel&&T("log","at pagesSub/pages/adv/buyList.nvue:292","\u7528\u6237\u70B9\u51FB\u53D6\u6D88")}})},U=(g,w)=>{T("log","at pagesSub/pages/adv/buyList.nvue:301","\u70B9\u51FB\u4E86\u8499\u5C42\uFF1A",g,w)},Z=()=>[{videoId:O.value.length+1,title:"\u3002",poster:"https://i02piccdn.sogoucdn.com/2acf176d90718d73",url:"https://txmov2.a.yximgs.com/upic/2020/11/08/19/BMjAyMDExMDgxOTQxNTlfNTIzNDczMzQ0XzM4OTQ1MDk5MTI4XzFfMw==_b_Bc770a92f0cf153407d60a2eddffeae2a.mp4",uploadTime:"2023-11-08 19:41",ipLocation:"\u4E0A\u6D77",author:{authorId:101,avatar:"https://i02piccdn.sogoucdn.com/2acf176d90718d73",nickName:"\u964C\u8DEF",genderName:"\u7537"}}],K=g=>{s.value=g};he(()=>{(0,a.toRaw)(r.value).ID==""&&(l.clear(),uni.reLaunch({url:"/pagesSub/pages/login/login"})),c.value==""&&uni.reLaunch({url:"/pages/my/my"})}),De(g=>{u()}),Ue(()=>{uni.navigateTo({url:"/pagesSub/pages/message/message"})}),(0,a.onMounted)(()=>{u()});let u=()=>xe(this,null,function*(){f.value.splice(0),v.value.splice(0);let g=(0,a.toRaw)(r.value);T("log","at pagesSub/pages/adv/buyList.nvue:410",g.ID),W.Adv.BuyList({userid:g.ID.toString()}).then(function(w){T("log","at pagesSub/pages/adv/buyList.nvue:414",w),w.Success&&w.ResData.length>0&&w.ResData.forEach(function(o,k){if(o.category=="small"&&(o.type=="SH"&&v.value.push({videoId:v.value.length+1,title:`\u63A5\u8D27\u6D88\u606F:
	\u5730\u5740:`+o.sh_address+`
	\u59D3\u540D:`+o.sh_name+`
	\u8054\u7CFB\u7535\u8BDD:`+o.sh_tel+`
	\u4EBA\u6570\u89C4\u6A21:`+o.sh_gm+`\u4EBA 
	\u64C5\u957F\u6B3E\u5F0F:`+o.sh_style+`
	\u5305\u6599\u80FD\u529B:`+o.sh_blnl+`
	\u6253\u7248\u5F00\u53D1\u80FD\u529B:`+o.sh_dbkfnl+`
	\u52A0\u5DE5\u96BE\u5EA6:`+o.sh_jgnd+`
	
	\u64AD\u653E\u6B21\u6570:99\u6B21
	\u64AD\u653E\u5217\u8868:\u5F20*\uFF1B\u6768**\uFF1B\u674E*
	`,poster:"https://img1.baidu.com/it/u=1314619043,374926406&fm=253&fmt=auto?w=348&h=500",url:W.Url.baseUrlBiz+"/"+o.sh_mat_path,uploadTime:"2024-10-02 09:41",ipLocation:"\u5317\u4EAC",author:{authorId:102,avatar:"https://i02piccdn.sogoucdn.com/2acf176d90718d73",nickName:"\u7BA1\u7406\u5458",genderName:"\u5973",tel:o.sh_tel}}),o.type=="FH"&&v.value.push({videoId:v.value.length+1,title:`\u53D1\u8D27\u6D88\u606F:
	\u8054\u7CFB\u7535\u8BDD:`+o.fh_Tel+`
	\u671F\u671B\u52A0\u5DE5\u5730\u5740:`+o.fh_address+`
	\u5DE5\u671F\u9650\u5236:`+o.fh_limitGQ+`
	\u57FA\u672C\u670D\u88C5\u7C7B\u578B:`+o.fh_fzType+`
	\u57FA\u672C\u5DE5\u827A\u6807\u51C6:`+o.fh_JBGYBZ+`
	\u8D26\u671F\u671F\u671B:`+o.fh_ZQQW+`
	\u8BA2\u5355\u6570\u91CF:`+o.fh_orderNum+`
	\u662F\u5426\u5305\u88C1:`+o.fh_SFBC+`
	\u662F\u5426\u5305\u9762\u8F85\u6599:`+o.fh_SFMLFZ+`
	
	\u64AD\u653E\u6B21\u6570:99\u6B21
	\u64AD\u653E\u5217\u8868:\u5F20*\uFF1B\u6768**\uFF1B\u674E*
	`,poster:"https://img1.baidu.com/it/u=1314619043,374926406&fm=253&fmt=auto?w=348&h=500",url:W.Url.baseUrlBiz+"/"+o.fh_mat_path,uploadTime:"2024-10-02 09:41",ipLocation:"\u5317\u4EAC",author:{authorId:102,avatar:"https://i02piccdn.sogoucdn.com/2acf176d90718d73",nickName:"\u7BA1\u7406\u5458",genderName:"\u5973",tel:o.fh_Tel}}),o.type=="ZG")){var _=JSON.parse(o.zg_zaopinList),x="";_.forEach(function(ee,re){x+=ee.type+"  \u62DB\u8058\u4EBA\u6570:"+ee.quantity+`\u4EBA
	`}),v.value.push({videoId:v.value.length+1,title:`\u62DB\u8058\u901A\u77E5:
	\u5730\u5740:`+o.zg_address+`
	\u59D3\u540D:`+o.zg_name+`
	\u8054\u7CFB\u7535\u8BDD:`+o.zg_tel+`
	\u62DB\u8058\u4FE1\u606F:
	`+x+`
	\u64AD\u653E\u6B21\u6570:99\u6B21
	\u64AD\u653E\u5217\u8868:\u5F20*\uFF1B\u6768**\uFF1B\u674E*
	`,poster:"https://img1.baidu.com/it/u=1314619043,374926406&fm=253&fmt=auto?w=348&h=500",url:W.Url.baseUrlBiz+"/"+o.zg_mat_path,uploadTime:"2024-10-02 09:41",ipLocation:"\u5317\u4EAC",author:{authorId:102,avatar:"https://i02piccdn.sogoucdn.com/2acf176d90718d73",nickName:"\u7BA1\u7406\u5458",genderName:"\u5973",tel:o.zg_tel}})}if(o.category=="big"&&(o.type=="SH"&&f.value.push({videoId:f.value.length+1,title:`\u63A5\u8D27\u6D88\u606F:
	\u5730\u5740:`+o.sh_address+`
	\u59D3\u540D:`+o.sh_name+`
	\u8054\u7CFB\u7535\u8BDD:`+o.sh_tel+`
	\u4EBA\u6570\u89C4\u6A21:`+o.sh_gm+`\u4EBA 
	\u64C5\u957F\u6B3E\u5F0F:`+o.sh_style+`
	\u5305\u6599\u80FD\u529B:`+o.sh_blnl+`
	\u6253\u7248\u5F00\u53D1\u80FD\u529B:`+o.sh_dbkfnl+`
	\u52A0\u5DE5\u96BE\u5EA6:`+o.sh_jgnd+`
	
	\u64AD\u653E\u6B21\u6570:99\u6B21
	\u64AD\u653E\u5217\u8868:\u5F20*\uFF1B\u6768**\uFF1B\u674E*
	`,poster:"https://img1.baidu.com/it/u=1314619043,374926406&fm=253&fmt=auto?w=348&h=500",url:W.Url.baseUrlBiz+"/"+o.sh_mat_path,uploadTime:"2024-10-02 09:41",ipLocation:"\u5317\u4EAC",author:{authorId:102,avatar:"https://i02piccdn.sogoucdn.com/2acf176d90718d73",nickName:"\u7BA1\u7406\u5458",genderName:"\u5973",tel:o.sh_tel}}),o.type=="FH"&&f.value.push({videoId:f.value.length+1,title:`\u53D1\u8D27\u6D88\u606F:
	\u8054\u7CFB\u7535\u8BDD:`+o.fh_Tel+`
	\u671F\u671B\u52A0\u5DE5\u5730\u5740:`+o.fh_address+`
	\u5DE5\u671F\u9650\u5236:`+o.fh_limitGQ+`
	\u57FA\u672C\u670D\u88C5\u7C7B\u578B:`+o.fh_fzType+`
	\u57FA\u672C\u5DE5\u827A\u6807\u51C6:`+o.fh_JBGYBZ+`
	\u8D26\u671F\u671F\u671B:`+o.fh_ZQQW+`
	\u8BA2\u5355\u6570\u91CF:`+o.fh_orderNum+`
	\u662F\u5426\u5305\u88C1:`+o.fh_SFBC+`
	\u662F\u5426\u5305\u9762\u8F85\u6599:`+o.fh_SFMLFZ+`
	
	\u64AD\u653E\u6B21\u6570:99\u6B21
	\u64AD\u653E\u5217\u8868:\u5F20*\uFF1B\u6768**\uFF1B\u674E*
	`,poster:"https://img1.baidu.com/it/u=1314619043,374926406&fm=253&fmt=auto?w=348&h=500",url:W.Url.baseUrlBiz+"/"+o.fh_mat_path,uploadTime:"2024-10-02 09:41",ipLocation:"\u5317\u4EAC",author:{authorId:102,avatar:"https://i02piccdn.sogoucdn.com/2acf176d90718d73",nickName:"\u7BA1\u7406\u5458",genderName:"\u5973",tel:o.fh_Tel}}),o.type=="ZG")){var _=JSON.parse(o.zg_zaopinList),x="";_.forEach(function(d,B){x+=d.type+"  \u62DB\u8058\u4EBA\u6570:"+d.quantity+`\u4EBA
	`}),f.value.push({videoId:f.value.length+1,title:`\u62DB\u8058\u901A\u77E5:
	\u5730\u5740:`+o.zg_address+`
	\u59D3\u540D:`+o.zg_name+`
	\u8054\u7CFB\u7535\u8BDD:`+o.zg_tel+`
	\u62DB\u8058\u4FE1\u606F:
	`+x+`
	\u64AD\u653E\u6B21\u6570:99\u6B21
	\u64AD\u653E\u5217\u8868:\u5F20*\uFF1B\u6768**\uFF1B\u674E*
	`,poster:"https://img1.baidu.com/it/u=1314619043,374926406&fm=253&fmt=auto?w=348&h=500",url:W.Url.baseUrlBiz+"/"+o.zg_mat_path,uploadTime:"2024-10-02 09:41",ipLocation:"\u5317\u4EAC",author:{authorId:102,avatar:"https://i02piccdn.sogoucdn.com/2acf176d90718d73",nickName:"\u7BA1\u7406\u5458",genderName:"\u5973",tel:o.zg_tel}})}})}.bind(this))});return(g,w)=>{let o=ge((0,a.resolveDynamicComponent)("ml-swiper"),qe);return(0,a.openBlock)(),(0,a.createElementBlock)("scroll-view",{scrollY:!0,showScrollbar:!0,enableBackToTop:!0,bubble:"true",style:{flexDirection:"column"}},[(0,a.createElementVNode)("view",{class:"tabsviewContent"},[(0,a.createElementVNode)("view",{class:"top_nav"},[(0,a.createElementVNode)("view",{style:(0,a.normalizeStyle)({height:(0,a.unref)(X)})},null,4),(0,a.createElementVNode)("view",{class:"top_content"},[(0,a.createElementVNode)("view",{class:"content_btn"},[(0,a.createElementVNode)("view",{class:"content_item",onClick:w[0]||(w[0]=k=>K(0))},[(0,a.createElementVNode)("u-text",{class:(0,a.normalizeClass)(["item_title",{i_on:s.value===0}])},"\u5927\u5587\u53ED",2),s.value==0?((0,a.openBlock)(),(0,a.createElementBlock)("view",{key:0,class:"line_on"})):((0,a.openBlock)(),(0,a.createElementBlock)("view",{key:1}))]),(0,a.createElementVNode)("view",{class:"content_item",onClick:w[1]||(w[1]=k=>K(1))},[(0,a.createElementVNode)("u-text",{class:(0,a.normalizeClass)(["item_title",{i_on:s.value===1}])},"\u5C0F\u5587\u53ED",2),s.value==1?((0,a.openBlock)(),(0,a.createElementBlock)("view",{key:0,class:"line_on"})):((0,a.openBlock)(),(0,a.createElementBlock)("view",{key:1}))])])])])]),s.value==0?((0,a.openBlock)(),(0,a.createElementBlock)("view",{key:0,class:"work_1"},[(0,a.createVNode)(o,{videoList:(0,a.unref)(f),width:(0,a.unref)(C),height:(0,a.unref)(A),bottomStyle:P,onLoadMore:z,onChange:R,onPlay:M,onPause:j,onEnded:V,onError:J,onWaiting:Q,onVideoClick:H,onDoubleClick:N,onMaskClick:U},{bottom:(0,a.withCtx)(({video:k,index:_})=>[k?((0,a.openBlock)(),(0,a.createElementBlock)("u-text",{key:0,class:"videoTitle"},(0,a.toDisplayString)(k==null?void 0:k.title),1)):(0,a.createCommentVNode)("",!0)]),_:1},8,["videoList","width","height"])])):(0,a.createCommentVNode)("",!0),s.value==1?((0,a.openBlock)(),(0,a.createElementBlock)("view",{key:1,class:"work_1"},[(0,a.createVNode)(o,{videoList:(0,a.unref)(v),width:(0,a.unref)(C),height:(0,a.unref)(A),bottomStyle:P,onLoadMore:z,onChange:R,onPlay:M,onPause:j,onEnded:V,onError:J,onWaiting:Q,onVideoClick:H,onDoubleClick:N,onMaskClick:U},{bottom:(0,a.withCtx)(({video:k,index:_})=>[k?((0,a.openBlock)(),(0,a.createElementBlock)("u-text",{key:0,class:"videoTitle"},(0,a.toDisplayString)(k==null?void 0:k.title),1)):(0,a.createCommentVNode)("",!0)]),_:1},8,["videoList","width","height"])])):(0,a.createCommentVNode)("",!0)])}}},se=le(Ot,[["styles",[Tt]]]);var ve=plus.webview.currentWebview();if(ve){let e=parseInt(ve.id),l="pagesSub/pages/adv/buyList",r={};try{r=JSON.parse(ve.__query__)}catch(b){}se.mpType="page";let c=Vue.createPageApp(se,{$store:getApp({allowDefault:!0}).$store,__pageId:e,__pagePath:l,__pageQuery:r});c.provide("__globalStyles",Vue.useCssStyles([...__uniConfig.styles,...se.styles||[]])),c.mount("#root")}})();
