# ml-player


### ml-player 介绍

> 1. 使用 `uniapp` 的原生 `video` 标签播放，解决了 `video` 在APP以及H5等 宽度定死的情况下，高度无法自适应的问题，以及 `video` 的层级问题。
> 2. `ml-player` 中增加了，`videoClick` 单击事件、`doubleClick` 双击事件、视频倍速、预览图等功能。
> 3. `ml-player`只对 `video` 做了增强，未改变 `uniapp` 中的原始方法。
> 4. 可以更改视频的展示形式。
>


### 使用说明

> 1. APP端需要配置manifest.json -> App模块配置 -> 勾选VideoPlay(视频播放)。
> 2. 运行到微信小程序videoSrc要为http/https开头。
>


### 安装方式

> 本组件符合[easycom](https://uniapp.dcloud.io/collocation/pages?id=easycom)规范，`HBuilderX 2.5.5`起，只需将本组件导入项目，在页面`template`中即可直接使用，无需在页面中`import`和注册`components`。



### 代码演示

```html
<template>
  <ml-player
    :video="videoInfo"
    :videoOptions="options"
    @play="videoPlay"
    @pause="videoPause"
    @videoClick="videoClick"
    @doubleClick="doubleClick"
  ></ml-player>
</template>

<script setup>
  import { ref } from 'vue';
  import { onLoad } from '@dcloudio/uni-app';
  
  const videoInfo = ref({}); // 视频信息配置项
  
  const winInfo = uni.getSystemInfoSync();
  const width = winInfo.windowWidth; // 设备宽度
  const height = winInfo.windowHeight; // 设备高度
  
  // 视频播放器配置项
  const options = { 
    // width,  // 播发器的宽度，不传与设备保持一致
    // height, // 播放器的高度，不传是设备高度的1/3
    enableClick: true, // 开始视频单击事件，默认false
    enableDblClick: true, // 开始视频的双击事件，默认false
    autoplay: false, // 不自动播放，默认true
    // controls: true, // 显示视频控制条，默认true
    fillHeight: true // 播放器的高度，填充播放器高度与设备同高，播放器高度占满全屏，默认false
  };
  
  const playing = ref(false); // 是否正在播放
  let videoContext = null; // 播放器上下文对象
  
  /**
   * 视频开始播放
   * @param { UniApp.VideoContext } context 播放器上下文对象
   */
  function videoPlay(context ){
    videoContext = context;
    playing.value = true;
  }
  
  /**
   * 视频暂停播放
   * @param { UniApp.VideoContext } context 播放器上下文对象
   */
  function videoPause(context){
    videoContext = context;
    playing.value = false;
  }
  
  /**
   * 点击了视频事件
   * @param { UniApp.VideoContext } context 播放器上下文对象
   * @param { Object } info 视频信息
   */
  function videoClick(context, info) {
    console.log("视频信息：", info)
    videoContext = context;
    if (playing.value) {
      videoContext?.pause();
    } else {
      videoContext?.play();
    }
  }
  
  /**
   * 视频双击事件
   * @param { UniApp.VideoContext } _context 播放器上下文对象
   * @param { Object } info 视频信息
   */
  function doubleClick(_context, info) {
    console.log(info)
    uni.showToast({ title: "双击了视频", icon: "none", mask: false });
  }

  /**
   * 请求接口，获取视频信息
   */
  const getVideoInfo = () => {
      // 模拟请求后台，获取到视频信息
    return 
      {
        videoId: list.value.length + 1,
        title: "抖音美女主播，JK超短裙学生妆美女跳舞展示，爱了爱了。",
        poster: "https://i02piccdn.sogoucdn.com/2acf176d90718d73",
        url: "https://txmov2.a.yximgs.com/upic/2020/11/08/19/BMjAyMDExMDgxOTQxNTlfNTIzNDczMzQ0XzM4OTQ1MDk5MTI4XzFfMw==_b_Bc770a92f0cf153407d60a2eddffeae2a.mp4",
        uploadTime: "2023-11-08 19:41",
        ipLocation: "上海",
        author: {
          authorId: 101,
          avatar: "https://i02piccdn.sogoucdn.com/4f85fc70df81d04a",
          nickName: "陌路",
          genderName: "男"
        }
      }
  }

  onLoad(() => {
    videoInfo.value = getVideoInfo();
  });
</script>

<style scoped lang="scss">
</style>
```



### props

| 属性名       | 类型    | 默认值   | 可选值   | 说明           | 必填 |
| ------------ | ------- | -------- | -------- | -------------- | ---- |
| showPlayer   | Boolean | true     | false    | 是否显示播放器 | 否   |
| video        | Object  | -见下文- | -见下文- | 视频资源配置项 | `是` |
| videoOptions | Object  | -见下文- | -见下文- | 播放器配置项   | 否   |
| danmu        | Object  | -见下文- | -见下文- | 弹幕配置项     | 否   |

**video：视频资源配置项**

| 属性名 | 类型   | 默认值 | 可选值 | 说明             | 必填 |
| ------ | ------ | ------ | ------ | ---------------- | ---- |
| url    | String | ""     | -      | 视频资源链接地址 | `是` |
| poster | String | ""     | -      | 预览图链接地址   | 否   |
| title  | String | ""     | -      | 视频资源标题     | 否   |

**videoOptions：播放器配置项**

80%的配置与uniapp中的保持一致，与 uniapp 中的配置保持一致：详见（https://uniapp.dcloud.net.cn/component/video.html）；
部分配置 uniapp 中不存在，为本组件新增的配置

| 属性名                    | 类型    | 默认值                                      | 可选值                                                    | 说明                                                         | 必填 |
| ------------------------- | ------- | ------------------------------------------- | --------------------------------------------------------- | ------------------------------------------------------------ | ---- |
| width                     | Number  | 设备宽度                                    | -                                                         | 组件宽度                                                     | 否   |
| height                    | Number  | 设备高度的1/3                               | -                                                         | 组件高度                                                     | 否   |
| fillHeight                | Boolean | false                                       | false                                                     | 是否填充高度，与设备等高                                     | 否   |
| controls                  | Boolean | true                                        | false                                                     | 是否显示默认播放控件                                         | 否   |
| autoplay                  | Boolean | true                                        | false                                                     | 是否自动播放                                                 | 否   |
| loop                      | Boolean | true                                        | false                                                     | 是否循环播放                                                 | 否   |
| muted                     | Boolean | false                                       | true                                                      | 是否静音播放                                                 | 否   |
| initialTime               | Number  | 0                                           | -                                                         | 指定视频初始播放位置 单位为秒（s）                           | 否   |
| duration                  | Number  | 0                                           | -                                                         | 指定视频长度，单位为秒（s）                                  | 否   |
| showPoster                | Boolean | true                                        | false                                                     | 显示预览图                                                   | 否   |
| showProgress              | Boolean | true                                        | false                                                     | 显示进度                                                     | 否   |
| showCenterPlayBtn         | Boolean | true                                        | false                                                     | 是否显示视频中间的播放按钮                                   | 否   |
| enablePlayGesture         | Boolean | false                                       | true                                                      | 是否开启播放手势，即双击切换播放/暂停                        | 否   |
| showLoading               | Boolean | true                                        | false                                                     | 是否显示loading控件                                          | 否   |
| enableProgressGesture     | Boolean | true                                        | false                                                     | 是否开启控制进度的手势                                       | 否   |
| objectFit                 | String  | "contain"                                   | contain：包含、fill：填充、cover：覆盖                    | 当视频大小与 video 容器大小不一致时，视频的表现形式          | 否   |
| playBtnPosition           | String  | "center"                                    | bottom、center                                            | 播放按钮的位置：bottom、center                               | 否   |
| mobilenetHintType         | Number  | 1                                           | 0                                                         | 移动网络提醒样式：0是不提醒，1是提醒，默认值为1              | 否   |
| autoPauseIfNavigate       | Boolean | true                                        | false                                                     | 微信：当跳转到其它小程序页面时，是否自动暂停本页面的视频     | 否   |
| autoPauseIfOpenNative     | Boolean | true                                        | false                                                     | 微信：当跳转到其它微信原生页面时，是否自动暂停本页面的视频   | 否   |
| vslideGesture             | Boolean | true                                        | false                                                     | 在非全屏模式下，是否开启亮度与音量调节手势（同 page-gesture） | 否   |
| codec                     | String  | "hardware"                                  |                                                           | 解码器选择：hardware：硬解码（硬解码可以增加解码算力，提高视频清晰度。）；software：ffmpeg软解码； | 否   |
| httpCache                 | Boolean | true                                        | false                                                     | 是否对 http、https 视频源开启本地缓存 （不适用于m3u8等流媒体协议） | 否   |
| playStrategy              | Number  | 2                                           | 播放策略：0：普通模式、1：平滑播放模式、2：M3U8优化模式； | 0-普通模式，适合绝大部分视频播放场景；1-平滑播放模式，增加缓冲区大小；2：M3U8优化模式，增加缓冲区大小； | 否   |
| header                    | Object  | {}                                          | -                                                         | HTTP 请求 Header                                             | 否   |
| isLive                    | Boolean | false                                       | true                                                      | 是否为直播源，App 3.7.2+、微信小程序（2.28.1+）              | 否   |
| showRate                  | Boolean | true                                        | false                                                     | 是否显示倍速按钮                                             | 否   |
| showFit                   | Boolean | true                                        | false                                                     | 是否显示展示形式                                             | 否   |
| rateList                  | Array   | ["0.5", "0.8", "1.0", "1.25", "1.5", "2.0"] | -                                                         | 视频倍速                                                     | 否   |
| enableClick               | Boolean | false                                       | true                                                      | 是否启用视频点击事件                                         | 否   |
| enableDblClick            | Boolean | false                                       | true                                                      | 是否启用视频双击事件                                         | 否   |
| showWaitingTips           | Boolean | true                                        | false                                                     | 显示视频缓冲提示                                             | 否   |
| waitingCount              | Number  | 5                                           | -                                                         | 缓冲次数等于该值时显示提示                                   | 否   |
| waitingMessage            | String  | "当前网络不佳"                              | -                                                         | 网络缓存提示信息                                             | 否   |
| vslideGestureInFullscreen | Boolean | true                                        | false                                                     | 在全屏模式下，是否开启亮度与音量调节手势                     | 否   |

**video：弹幕配置项**

与 uniapp 中的配置保持一致：详见（https://uniapp.dcloud.net.cn/component/video.html）

| 属性名      | 类型    | 默认值                                               | 可选值 | 说明                                                         | 必填 |
| ----------- | ------- | ---------------------------------------------------- | ------ | ------------------------------------------------------------ | ---- |
| danmuList   | Array   | [{text:"弹幕内容",color:"字体颜色",time:"出现时间"}] | -      | 弹幕信息列表 { text: '第1s出现的弹幕，红色字体', color: '#ff0000', time: 1} | 否   |
| danmuBtn    | Boolean | false                                                | true   | 是否显示弹幕按钮，只在初始化时有效，不能动态变更             | 否   |
| enableDanmu | Boolean | false                                                | true   | 是否展示弹幕，只在初始化时有效，不能动态变更                 | 否   |
|             |         |                                                      |        |                                                              |      |

### 事件 Events

事件参数的上下文对象与uniapp的保持一致，详见（https://uniapp.dcloud.net.cn/api/media/video-context.html#createvideocontext）
事件参数中的 event 与 uniapp 中的保持一致：详见（https://uniapp.dcloud.net.cn/component/video.html）
事件参数中的 video 与 props 中的入参 video 保持一致

| 事件名            | 返回参数       | 说明                                                         |
| ----------------- | -------------- | ------------------------------------------------------------ |
| @play             | context        | 【视频播放时触发】context 为 video 的上下文对象，可以操作 video 组件 |
| @pause            | context        | 【视频暂停时触发】context 为 video 的上下文对象；            |
| @ended            | context        | 【视频结束时触发】context 为 video 的上下文对象；            |
| @error            | context，event | 【视频出错时触发】context 为 video 的上下文对象；event 与 uniapp 中的保持一致； |
| @waiting          | context        | 【视频缓冲时触发】context 为 video 的上下文对象；            |
| @timeupdate       | context，event | 【进度条变化时触发】context 为 video 的上下文对象；event 与 uniapp 中的保持一致； |
| @fullscreenchange | context，event | 【当视频进入和退出全屏时触发】context 为 video 的上下文对象；event 与 uniapp 中的保持一致； |
| @loadedmetadata   | context，event | 【视频元数据加载完成时触发】context 为 video 的上下文对象；event 与 uniapp 中的保持一致； |
| @fullscreenclick  | context，event | 【视频播放全屏播放时点击事件】context 为 video 的上下文对象；event 与 uniapp 中的保持一致； |
| @controlstoggle   | context，event | 【切换 controls 显示隐藏时 触发此事件】context 为 video 的上下文对象；event 与 uniapp 中的保持一致； |
| @videoClick       | context，video | 【视频单击事件，需要在 `props.videoOptions.enableClick`设置为`true` 】context 为 video 的上下文对象；video 视频资源配置项数据信息 |
| @doubleClick      | context，video | 【视频双击事件，需要在 `props.videoOptions.enableDblClick`设置为`true` 】context 为 video 的上下文对象；video 视频资源配置项数据信息，APP端不支持 |
| @playVideo        | context        | 【点击中间的播放按钮时触发】context 为 video 的上下文对象；  |
| @progress         | context，event | 【加载进度变化时触发】context 为 video 的上下文对象；event 与 uniapp 中的保持一致； |





### 注意事项

- APP需要按照文档进行配置。
- APP端单击事件，需要使用`.nvue`文件。
- APP端需要配置manifest.json -> App模块配置 -> 勾选VideoPlay(视频播放)。