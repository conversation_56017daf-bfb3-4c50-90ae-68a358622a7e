import { _ as _export_sfc, u as userInfoStore, o as onShow, a as onLoad, c as onNavigationBarButtonTap, f as formatAppLog, d as apis, r as resolveEasycom, e as __easycom_0 } from "../../../index.js";
import { ref, toRaw, onMounted, resolveDynamicComponent, openBlock, createElementBlock, createCommentVNode, createElementVNode, normalizeStyle, normalizeClass, createVNode, withCtx, toDisplayString } from "vue";
import { storeToRefs } from "pinia";
const _style_0 = { "imgV": { "": { "width": 100, "textAlign": "center", "minHeight": 65 } }, "work": { "": { "backgroundColor": "#ffffff", "marginTop": "10rpx", "marginRight": "10rpx", "marginBottom": "10rpx", "marginLeft": "10rpx", "borderRadius": 6 } }, "title": { ".work ": { "display": "flex", "alignItems": "center", "height": "80rpx", "paddingTop": 0, "paddingRight": "20rpx", "paddingBottom": 0, "paddingLeft": "20rpx", "borderBottomWidth": 1, "borderBottomStyle": "solid", "borderBottomColor": "#e5e7eb" } }, "menu": { ".work ": { "gridTemplateColumns": "repeat(3, minmax(0, 1fr))", "gap": "30rpx", "paddingTop": "30rpx", "paddingRight": 0, "paddingBottom": "30rpx", "paddingLeft": 0, "fontSize": "24rpx", "textAlign": "center" } }, "item": { ".work .menu ": { "color": "#999999" }, ".work .menu_1 ": { "color": "#999999", "textAlign": "center" }, ".search ": { "display": "flex", "alignItems": "center", "gap": "20rpx", "fontSize": "28rpx" } }, "img": { ".work .menu .item ": { "height": "128rpx", "width": "128rpx", "borderRadius": 6, "marginTop": 0, "marginBottom": "10rpx" }, ".work .menu_1 .item ": { "height": "100rpx", "width": "100rpx", "borderRadius": 6, "marginTop": "10rpx", "marginRight": "10rpx", "marginBottom": "20rpx", "marginLeft": "10rpx" }, ".top_nav .top_content ": { "width": "44rpx", "height": "44rpx" } }, "menu_1": { ".work ": { "gridTemplateColumns": "repeat(4, minmax(0, 1fr))", "gap": "30rpx", "paddingTop": "30rpx", "paddingRight": 0, "paddingBottom": "30rpx", "paddingLeft": 0, "fontSize": "24rpx", "textAlign": "center" } }, "search": { "": { "gridTemplateColumns": "repeat(1, minmax(0, 1fr))", "paddingTop": "20rpx", "paddingRight": "20rpx", "paddingBottom": "20rpx", "paddingLeft": "20rpx", "gap": "20rpx", "backgroundColor": "#ffffff" }, ".top_nav .top_content ": { "position": "absolute", "right": 20, "top": "35rpx" } }, "label": { ".search .item ": { "flexShrink": 0 } }, "input": { ".search .item ": { "flex": 1, "backgroundColor": "#f8f8f8", "borderRadius": "10rpx", "paddingTop": 0, "paddingRight": "20rpx", "paddingBottom": 0, "paddingLeft": "20rpx", "minHeight": "60rpx" } }, "value": { ".search .item .input ": { "display": "flex", "alignItems": "center", "justifyContent": "space-between", "minHeight": "60rpx", "fontFamily::after": '"iconfont"', "content::after": '"\\e840"', "color::after": "#999999", "content:empty::before": '"请选择"', "color:empty::before": "#808080" } }, "submit": { ".search ": { "backgroundColor": "#007aff", "color": "#ffffff" } }, "btnGroup": { ".search ": { "!width": "100rpx" } }, "submit_deploy": { "": { "position": "fixed", "left": "20rpx", "right": "20rpx", "backgroundColor": "#007aff", "color": "#ffffff", "height": "80rpx", "bottom": "10rpx" } }, "person-head": { "": { "position": "relative", "backgroundColor": "#ffffff", "marginLeft": "20rpx", "marginRight": "20rpx" } }, "videoTitle": { "": { "paddingTop": 5, "paddingRight": 5, "paddingBottom": 5, "paddingLeft": 5, "color": "#de4a00", "fontSize": 13, "lines": 13, "whiteSpace": "normal" } }, "tabsviewContent": { "": { "position": "fixed", "!minWidth": "750rpx" } }, "tabsview": { "": { "!minWidth": "750rpx" } }, "top_nav": { "": { "position": "fixed", "top": 0, "left": 0, "right": 0, "backgroundImage": "linear-gradient(to bottom, rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0))" } }, "top_content": { ".top_nav ": { "paddingTop": "30rpx", "flexDirection": "row", "alignItems": "center", "justifyContent": "center" } }, "player": { ".top_nav .top_content ": { "position": "absolute", "left": 20, "top": "35rpx" } }, "content_btn": { ".top_nav .top_content ": { "flexDirection": "row", "width": 220, "alignItems": "center", "justifyContent": "space-around" } }, "content_item": { ".top_nav .top_content .content_btn ": { "position": "relative", "height": 30 } }, "line_on": { ".top_nav .top_content .content_btn .content_item ": { "position": "absolute", "width": 50, "height": 2, "backgroundColor": "#FFFFFF", "bottom": 0, "left": 2, "borderRadius": "4rpx" } }, "item_title": { ".top_nav .top_content .content_btn .content_item ": { "color": "#dcdcdc", "fontSize": "36rpx", "fontWeight": "bold" } }, "i_on": { ".top_nav .top_content .content_btn .content_item ": { "fontWeight": "bold", "fontSize": "38rpx", "!color": "#FFFFFF" } } };
const effect3d = true;
const effect3dMargin = 40;
const autoplay = false;
const vertical = false;
const fullScreen = true;
const topFloat = true;
const fotterFloat = true;
const mode = "round";
const indicatorPos = "bottomCenter";
const dotIndex = 0;
const dotFloatIndex = 0;
const _sfc_main = {
  __name: "buyList",
  setup(__props, { expose: __expose }) {
    __expose();
    const store = userInfoStore();
    const {
      user,
      roleID,
      teamID
    } = storeToRefs(store);
    const current = ref(0);
    const tabs = ["大喇叭", "小喇叭"];
    let listSmall = ref([]);
    let listBig = ref([]);
    const list = ref([
      {
        type: "video",
        topTip: "这是一个小喇叭----底部提示",
        poster: "https://img1.baidu.com/it/u=1314619043,374926406&fm=253&fmt=auto?w=348&h=500",
        src: "http://www.w3school.com.cn/example/html5/mov_bbb.mp4",
        bottomTip: ""
        //'这是一个小喇叭----底部提示',
      },
      {
        type: "image",
        topTip: "这是一个小喇叭----底部提示",
        src: "https://img2.baidu.com/it/u=3256616248,1972425356&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=1039"
      },
      {
        type: "image",
        topTip: "这是一个小喇叭----底部提示",
        src: "https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fb-ssl.duitang.com%2Fuploads%2Fitem%2F201707%2F23%2F20170723111737_JZGmC.thumb.700_0.jpeg&refer=http%3A%2F%2Fb-ssl.duitang.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1725513912&t=01acc1e9efcbfacd570fed69edd5a9aa"
      },
      {
        type: "video",
        topTip: "这是一个小喇叭----底部提示",
        currentTime: 120,
        //初始帧时间---默认缓存存储
        poster: "https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fss2.meipian.me%2Fusers%2F3107464%2F269b3689a58b4e1f8eb7a882eae338fb.jpg%3Fmeipian-raw%2Fbucket%2Fivwen%2Fkey%2FdXNlcnMvMzEwNzQ2NC8yNjliMzY4OWE1OGI0ZTFmOGViN2E4ODJlYWUzMzhmYi5qcGc%3D%2Fsign%2F203e153600702e8c1f73d97470fd9234.jpg&refer=http%3A%2F%2Fss2.meipian.me&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1725513912&t=158a229b97ec18cbcc9a07aabc00e685",
        src: "https://www.w3schools.com/html/movie.mp4"
      }
    ]);
    const ad = ref([]);
    let styleName = ref("");
    const groupList = ref([]);
    const win = uni.getSystemInfoSync();
    const width = win.windowWidth;
    const height = win.windowHeight;
    const bottomStyle = {
      // "position": "absolute",
      "top": "36px"
      // "left": "0",
      // "display": "flex",
      // "flex-wrap": "wrap",
      // "flex-direction": "column"
    };
    const realList = ref([]);
    const currentVedio = ref(0);
    let context = null;
    const counter = ref(0);
    let windowWidth = 0;
    let statusBarHeight = 0;
    const onchange = (index, size) => {
      formatAppLog("log", "at pagesSub/pages/adv/buyList.nvue:195", "onchange-当前索引:", index + "aa" + size);
      currentVedio.value = index;
      if (index == 0) {
        uni.showToast({ title: "当前已是第一个视频", icon: "none", mask: false });
      }
      if (index == size - 1) {
        uni.showToast({ title: "当前已是最后一个视频", icon: "none", mask: false });
      }
    };
    const loadMore = (index, size) => {
      formatAppLog("log", "at pagesSub/pages/adv/buyList.nvue:207", "加载更所视频：", index + " / " + size);
      if (counter.value > 5)
        return;
      getList().forEach((item) => {
        item.title = realList.value.length + "，" + item.title + item.title + item.title;
        realList.value.push(item);
      });
      counter.value = counter.value + 1;
    };
    const play = (context2) => {
      formatAppLog("log", "at pagesSub/pages/adv/buyList.nvue:221", "视频开始播放");
    };
    const pause = (context2) => {
      formatAppLog("log", "at pagesSub/pages/adv/buyList.nvue:228", "视频暂停播放");
    };
    const ended = (context2) => {
      formatAppLog("log", "at pagesSub/pages/adv/buyList.nvue:235", "视频播放结束");
    };
    const error = (context2, event) => {
      formatAppLog("error", "at pagesSub/pages/adv/buyList.nvue:242", "视频播放出错：", event);
    };
    const waiting = (context2) => {
      formatAppLog("error", "at pagesSub/pages/adv/buyList.nvue:249", "视频出现缓冲");
    };
    const videoClick = (context2, video) => {
      formatAppLog("log", "at pagesSub/pages/adv/buyList.nvue:256", "点击了视频：", video);
    };
    const doubleClick = (context2, video) => {
      phone.value = video.author.tel;
      formatAppLog("log", "at pagesSub/pages/adv/buyList.nvue:264", "双击了视频：", video);
      uni.showModal({
        title: "提示",
        content: "拨打电话给发布人？",
        confirmText: "拨打",
        cancelText: "取消",
        success: function(res) {
          if (res.confirm) {
            uni.makePhoneCall({
              phoneNumber: phone.value
            });
          } else if (res.cancel) {
            formatAppLog("log", "at pagesSub/pages/adv/buyList.nvue:292", "用户点击取消");
          }
        }
      });
    };
    const maskClick = (index, video) => {
      context = context;
      formatAppLog("log", "at pagesSub/pages/adv/buyList.nvue:301", "点击了蒙层：", index, video);
    };
    const getList = () => {
      return [{
        videoId: realList.value.length + 1,
        title: "。",
        poster: "https://i02piccdn.sogoucdn.com/2acf176d90718d73",
        url: "https://txmov2.a.yximgs.com/upic/2020/11/08/19/BMjAyMDExMDgxOTQxNTlfNTIzNDczMzQ0XzM4OTQ1MDk5MTI4XzFfMw==_b_Bc770a92f0cf153407d60a2eddffeae2a.mp4",
        uploadTime: "2023-11-08 19:41",
        ipLocation: "上海",
        author: {
          authorId: 101,
          avatar: "https://i02piccdn.sogoucdn.com/2acf176d90718d73",
          nickName: "陌路",
          genderName: "男"
        }
      }];
    };
    const changeEvent = (e) => {
      current.value = e;
    };
    onShow(() => {
      let userInfo = toRaw(user.value);
      if (userInfo.ID == "") {
        store.clear();
        uni.reLaunch({
          url: "/pagesSub/pages/login/login"
        });
      }
      if (roleID.value == "") {
        uni.reLaunch({
          url: "/pages/my/my"
        });
      }
    });
    onLoad((e) => {
      getAdv();
    });
    onNavigationBarButtonTap(() => {
      uni.navigateTo({
        url: "/pagesSub/pages/message/message"
      });
    });
    onMounted(() => {
      getAdv();
    });
    const handleAdClick = (position) => {
      formatAppLog("log", "at pagesSub/pages/adv/buyList.nvue:354", "handleAdClick " + position);
    };
    const changeTab = (index) => {
      formatAppLog("log", "at pagesSub/pages/adv/buyList.nvue:360", index);
    };
    const resetEvent = () => {
      styleName.value = "";
      getGroup();
    };
    const searchEvent = () => {
      getGroup();
    };
    const publishEvent = () => {
      uni.removeStorageSync("storage_orderinfo");
      if (current.value == 0) {
        uni.navigateTo({
          url: "/pagesSub/pages/adv/publishFH"
        });
      }
      if (current.value == 1) {
        uni.navigateTo({
          url: "/pagesSub/pages/adv/publishZG"
        });
      }
      if (current.value == 2) {
        uni.navigateTo({
          url: "/pagesSub/pages/adv/publishSH"
        });
      }
    };
    const getGroup = async () => {
      let r = await apis.Chat.GroupList({
        groupId: "",
        groupName: styleName.value
      });
      formatAppLog("log", "at pagesSub/pages/adv/buyList.nvue:396", r);
      groupList.value.splice(0);
      if (r.ResData.length > 0) {
        r.ResData.forEach(function(item, index) {
          groupList.value.push(item);
        });
      }
    };
    const getAdv = async () => {
      listBig.value.splice(0);
      listSmall.value.splice(0);
      let userInfo = toRaw(user.value);
      formatAppLog("log", "at pagesSub/pages/adv/buyList.nvue:410", userInfo.ID);
      apis.Adv.BuyList({
        userid: userInfo.ID.toString()
      }).then((function(res) {
        formatAppLog("log", "at pagesSub/pages/adv/buyList.nvue:414", res);
        if (res.Success && res.ResData.length > 0) {
          res.ResData.forEach(function(item, index) {
            if (item.category == "small") {
              if (item.type == "SH") {
                listSmall.value.push({
                  videoId: listSmall.value.length + 1,
                  title: "接货消息:\n	地址:" + item.sh_address + "\n	姓名:" + item.sh_name + "\n	联系电话:" + item.sh_tel + "\n	人数规模:" + item.sh_gm + "人 \n	擅长款式:" + item.sh_style + "\n	包料能力:" + item.sh_blnl + "\n	打版开发能力:" + item.sh_dbkfnl + "\n	加工难度:" + item.sh_jgnd + "\n	\n	播放次数:99次\n	播放列表:张*；杨**；李*\n	",
                  poster: "https://img1.baidu.com/it/u=1314619043,374926406&fm=253&fmt=auto?w=348&h=500",
                  //'https://i02piccdn.sogoucdn.com/2acf176d90718d73",
                  url: apis.Url.baseUrlBiz + "/" + item.sh_mat_path,
                  uploadTime: "2024-10-02 09:41",
                  ipLocation: "北京",
                  author: {
                    authorId: 102,
                    avatar: "https://i02piccdn.sogoucdn.com/2acf176d90718d73",
                    nickName: "管理员",
                    genderName: "女",
                    tel: item.sh_tel
                  }
                });
              }
              if (item.type == "FH") {
                listSmall.value.push({
                  videoId: listSmall.value.length + 1,
                  title: "发货消息:\n	联系电话:" + item.fh_Tel + "\n	期望加工地址:" + item.fh_address + "\n	工期限制:" + item.fh_limitGQ + "\n	基本服装类型:" + item.fh_fzType + "\n	基本工艺标准:" + item.fh_JBGYBZ + "\n	账期期望:" + item.fh_ZQQW + "\n	订单数量:" + item.fh_orderNum + "\n	是否包裁:" + item.fh_SFBC + "\n	是否包面辅料:" + item.fh_SFMLFZ + "\n	\n	播放次数:99次\n	播放列表:张*；杨**；李*\n	",
                  poster: "https://img1.baidu.com/it/u=1314619043,374926406&fm=253&fmt=auto?w=348&h=500",
                  //'https://i02piccdn.sogoucdn.com/2acf176d90718d73",
                  url: apis.Url.baseUrlBiz + "/" + item.fh_mat_path,
                  uploadTime: "2024-10-02 09:41",
                  ipLocation: "北京",
                  author: {
                    authorId: 102,
                    avatar: "https://i02piccdn.sogoucdn.com/2acf176d90718d73",
                    nickName: "管理员",
                    genderName: "女",
                    tel: item.fh_Tel
                  }
                });
              }
              if (item.type == "ZG") {
                var zhaopinList = JSON.parse(item.zg_zaopinList);
                var gwList = "";
                zhaopinList.forEach(function(item1, index2) {
                  gwList += item1.type + "  招聘人数:" + item1.quantity + "人\n	";
                });
                listSmall.value.push({
                  videoId: listSmall.value.length + 1,
                  title: "招聘通知:\n	地址:" + item.zg_address + "\n	姓名:" + item.zg_name + "\n	联系电话:" + item.zg_tel + "\n	招聘信息:\n	" + gwList + "\n	播放次数:99次\n	播放列表:张*；杨**；李*\n	",
                  poster: "https://img1.baidu.com/it/u=1314619043,374926406&fm=253&fmt=auto?w=348&h=500",
                  //'https://i02piccdn.sogoucdn.com/2acf176d90718d73",
                  url: apis.Url.baseUrlBiz + "/" + item.zg_mat_path,
                  uploadTime: "2024-10-02 09:41",
                  ipLocation: "北京",
                  author: {
                    authorId: 102,
                    avatar: "https://i02piccdn.sogoucdn.com/2acf176d90718d73",
                    nickName: "管理员",
                    genderName: "女",
                    tel: item.zg_tel
                  }
                });
              }
            }
            if (item.category == "big") {
              if (item.type == "SH") {
                listBig.value.push({
                  videoId: listBig.value.length + 1,
                  title: "接货消息:\n	地址:" + item.sh_address + "\n	姓名:" + item.sh_name + "\n	联系电话:" + item.sh_tel + "\n	人数规模:" + item.sh_gm + "人 \n	擅长款式:" + item.sh_style + "\n	包料能力:" + item.sh_blnl + "\n	打版开发能力:" + item.sh_dbkfnl + "\n	加工难度:" + item.sh_jgnd + "\n	\n	播放次数:99次\n	播放列表:张*；杨**；李*\n	",
                  poster: "https://img1.baidu.com/it/u=1314619043,374926406&fm=253&fmt=auto?w=348&h=500",
                  //'https://i02piccdn.sogoucdn.com/2acf176d90718d73",
                  url: apis.Url.baseUrlBiz + "/" + item.sh_mat_path,
                  uploadTime: "2024-10-02 09:41",
                  ipLocation: "北京",
                  author: {
                    authorId: 102,
                    avatar: "https://i02piccdn.sogoucdn.com/2acf176d90718d73",
                    nickName: "管理员",
                    genderName: "女",
                    tel: item.sh_tel
                  }
                });
              }
              if (item.type == "FH") {
                listBig.value.push({
                  videoId: listBig.value.length + 1,
                  title: "发货消息:\n	联系电话:" + item.fh_Tel + "\n	期望加工地址:" + item.fh_address + "\n	工期限制:" + item.fh_limitGQ + "\n	基本服装类型:" + item.fh_fzType + "\n	基本工艺标准:" + item.fh_JBGYBZ + "\n	账期期望:" + item.fh_ZQQW + "\n	订单数量:" + item.fh_orderNum + "\n	是否包裁:" + item.fh_SFBC + "\n	是否包面辅料:" + item.fh_SFMLFZ + "\n	\n	播放次数:99次\n	播放列表:张*；杨**；李*\n	",
                  poster: "https://img1.baidu.com/it/u=1314619043,374926406&fm=253&fmt=auto?w=348&h=500",
                  //'https://i02piccdn.sogoucdn.com/2acf176d90718d73",
                  url: apis.Url.baseUrlBiz + "/" + item.fh_mat_path,
                  uploadTime: "2024-10-02 09:41",
                  ipLocation: "北京",
                  author: {
                    authorId: 102,
                    avatar: "https://i02piccdn.sogoucdn.com/2acf176d90718d73",
                    nickName: "管理员",
                    genderName: "女",
                    tel: item.fh_Tel
                  }
                });
              }
              if (item.type == "ZG") {
                var zhaopinList = JSON.parse(item.zg_zaopinList);
                var gwList = "";
                zhaopinList.forEach(function(item1, index2) {
                  gwList += item1.type + "  招聘人数:" + item1.quantity + "人\n	";
                });
                listBig.value.push({
                  videoId: listBig.value.length + 1,
                  title: "招聘通知:\n	地址:" + item.zg_address + "\n	姓名:" + item.zg_name + "\n	联系电话:" + item.zg_tel + "\n	招聘信息:\n	" + gwList + "\n	播放次数:99次\n	播放列表:张*；杨**；李*\n	",
                  poster: "https://img1.baidu.com/it/u=1314619043,374926406&fm=253&fmt=auto?w=348&h=500",
                  //'https://i02piccdn.sogoucdn.com/2acf176d90718d73",
                  url: apis.Url.baseUrlBiz + "/" + item.zg_mat_path,
                  uploadTime: "2024-10-02 09:41",
                  ipLocation: "北京",
                  author: {
                    authorId: 102,
                    avatar: "https://i02piccdn.sogoucdn.com/2acf176d90718d73",
                    nickName: "管理员",
                    genderName: "女",
                    tel: item.zg_tel
                  }
                });
              }
            }
          });
        }
      }).bind(this));
    };
    const __returned__ = { store, user, roleID, teamID, current, tabs, effect3d, effect3dMargin, autoplay, vertical, fullScreen, topFloat, fotterFloat, mode, indicatorPos, get listSmall() {
      return listSmall;
    }, set listSmall(v) {
      listSmall = v;
    }, get listBig() {
      return listBig;
    }, set listBig(v) {
      listBig = v;
    }, list, dotIndex, dotFloatIndex, ad, get styleName() {
      return styleName;
    }, set styleName(v) {
      styleName = v;
    }, groupList, win, width, height, bottomStyle, realList, currentVedio, get context() {
      return context;
    }, set context(v) {
      context = v;
    }, counter, get windowWidth() {
      return windowWidth;
    }, set windowWidth(v) {
      windowWidth = v;
    }, get statusBarHeight() {
      return statusBarHeight;
    }, set statusBarHeight(v) {
      statusBarHeight = v;
    }, onchange, loadMore, play, pause, ended, error, waiting, videoClick, doubleClick, maskClick, getList, changeEvent, handleAdClick, changeTab, resetEvent, searchEvent, publishEvent, getGroup, getAdv, ref, toRaw, onMounted, get onLoad() {
      return onLoad;
    }, get onShow() {
      return onShow;
    }, get onNavigationBarButtonTap() {
      return onNavigationBarButtonTap;
    }, get Https() {
      return apis;
    }, get userInfoStore() {
      return userInfoStore;
    }, get storeToRefs() {
      return storeToRefs;
    } };
    Object.defineProperty(__returned__, "__isScriptSetup", { enumerable: false, value: true });
    return __returned__;
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  const _component_ml_swiper = resolveEasycom(resolveDynamicComponent("ml-swiper"), __easycom_0);
  return openBlock(), createElementBlock("scroll-view", {
    scrollY: true,
    showScrollbar: true,
    enableBackToTop: true,
    bubble: "true",
    style: { flexDirection: "column" }
  }, [
    createCommentVNode(' <v-tabs v-model="current" :scroll="false" :tabs="tabs"></v-tabs> '),
    createCommentVNode(' <v-tabs v-model="current" :tabs="tabs" :pills="true" line-height="0" pillsColor="#007aff" activeColor="#fff" @change="changeTab"></v-tabs> '),
    createElementVNode("view", { class: "tabsviewContent" }, [
      createCommentVNode(` <v-tabs class="tabsview" v-model="current" :scroll="false" \r
		:tabs="tabs" :bgColor='bgColor' :lineColor="lineColor"\r
		@change="changeEvent"></v-tabs> `),
      createElementVNode("view", { class: "top_nav" }, [
        createElementVNode(
          "view",
          {
            style: normalizeStyle({ height: $setup.statusBarHeight })
          },
          null,
          4
          /* STYLE */
        ),
        createElementVNode("view", { class: "top_content" }, [
          createElementVNode("view", { class: "content_btn" }, [
            createElementVNode("view", {
              class: "content_item",
              onClick: _cache[0] || (_cache[0] = ($event) => $setup.changeEvent(0))
            }, [
              createElementVNode(
                "u-text",
                {
                  class: normalizeClass(["item_title", { "i_on": $setup.current === 0 }])
                },
                "大喇叭",
                2
                /* CLASS */
              ),
              $setup.current == 0 ? (openBlock(), createElementBlock("view", {
                key: 0,
                class: "line_on"
              })) : (openBlock(), createElementBlock("view", { key: 1 }))
            ]),
            createElementVNode("view", {
              class: "content_item",
              onClick: _cache[1] || (_cache[1] = ($event) => $setup.changeEvent(1))
            }, [
              createElementVNode(
                "u-text",
                {
                  class: normalizeClass(["item_title", { "i_on": $setup.current === 1 }])
                },
                "小喇叭",
                2
                /* CLASS */
              ),
              $setup.current == 1 ? (openBlock(), createElementBlock("view", {
                key: 0,
                class: "line_on"
              })) : (openBlock(), createElementBlock("view", { key: 1 }))
            ])
          ])
        ])
      ])
    ]),
    $setup.current == 0 ? (openBlock(), createElementBlock("view", {
      key: 0,
      class: "work_1"
    }, [
      createCommentVNode(" <zSwiper\r\n			:autoplay='autoplay'\r\n			:list='listBig'\r\n			:effect3d='effect3d' \r\n			:fullScreen='fullScreen' \r\n			:effect3dMargin='`${effect3dMargin}rpx`' \r\n			:vertical='vertical'\r\n			:topFloat='topFloat'\r\n			:fotterFloat='fotterFloat'\r\n			:mode='mode'\r\n			:indicatorPos='indicatorPos'\r\n			></zSwiper> "),
      createVNode(_component_ml_swiper, {
        videoList: $setup.listBig,
        width: $setup.width,
        height: $setup.height,
        bottomStyle: $setup.bottomStyle,
        onLoadMore: $setup.loadMore,
        onChange: $setup.onchange,
        onPlay: $setup.play,
        onPause: $setup.pause,
        onEnded: $setup.ended,
        onError: $setup.error,
        onWaiting: $setup.waiting,
        onVideoClick: $setup.videoClick,
        onDoubleClick: $setup.doubleClick,
        onMaskClick: $setup.maskClick
      }, {
        bottom: withCtx(({ video, index }) => [
          createCommentVNode(" 视频标题 "),
          video ? (openBlock(), createElementBlock(
            "u-text",
            {
              key: 0,
              class: "videoTitle"
            },
            toDisplayString(video == null ? void 0 : video.title),
            1
            /* TEXT */
          )) : createCommentVNode("v-if", true)
        ]),
        _: 1
        /* STABLE */
      }, 8, ["videoList", "width", "height"])
    ])) : createCommentVNode("v-if", true),
    $setup.current == 1 ? (openBlock(), createElementBlock("view", {
      key: 1,
      class: "work_1"
    }, [
      createCommentVNode(' <image class="imgV" src="https://img1.baidu.com/it/u=4232351783,2577170786&fm=253&fmt=auto?w=333&h=500"></image> '),
      createCommentVNode(" <zSwiper\r\n			:autoplay='autoplay'\r\n			:list='listSmall'\r\n			:effect3d='effect3d' \r\n			:fullScreen='fullScreen' \r\n			:effect3dMargin='`${effect3dMargin}rpx`' \r\n			:vertical='vertical'\r\n			:topFloat='topFloat'\r\n			:fotterFloat='fotterFloat'\r\n			:mode='mode'\r\n			:indicatorPos='indicatorPos'\r\n			></zSwiper> "),
      createVNode(_component_ml_swiper, {
        videoList: $setup.listSmall,
        width: $setup.width,
        height: $setup.height,
        bottomStyle: $setup.bottomStyle,
        onLoadMore: $setup.loadMore,
        onChange: $setup.onchange,
        onPlay: $setup.play,
        onPause: $setup.pause,
        onEnded: $setup.ended,
        onError: $setup.error,
        onWaiting: $setup.waiting,
        onVideoClick: $setup.videoClick,
        onDoubleClick: $setup.doubleClick,
        onMaskClick: $setup.maskClick
      }, {
        bottom: withCtx(({ video, index }) => [
          createCommentVNode(" 视频标题 "),
          video ? (openBlock(), createElementBlock(
            "u-text",
            {
              key: 0,
              class: "videoTitle"
            },
            toDisplayString(video == null ? void 0 : video.title),
            1
            /* TEXT */
          )) : createCommentVNode("v-if", true)
        ]),
        _: 1
        /* STABLE */
      }, 8, ["videoList", "width", "height"])
    ])) : createCommentVNode("v-if", true)
  ]);
}
const buyList = /* @__PURE__ */ _export_sfc(_sfc_main, [["render", _sfc_render], ["styles", [_style_0]], ["__file", "C:/Users/<USER>/Desktop/打印机/App源码/App/pagesSub/pages/adv/buyList.nvue"]]);
export {
  buyList as default
};
