<template>
	<view class="pagination">
		<view class="total">总计 {{totalJian}} 件</view>
		<!-- <view class="total">总计 {{totalJian}} 件，完成 {{totalPersent}}%</view> -->
		  <view class="totalTitle">
			  <view class="scrollPagination1" >
				  <view class="totalTitleSub1">按颜色打印</view>
			  </view>
			  <scroll-view  class="scrollPagination" scroll-y="true" scroll-x="false">
				  <view class="totalTitleSub" v-for="item in TotalList" :key="item.ID">
					  <checkbox @click="handleTap(item)"></checkbox>
						{{ item.color }} - <text class="txtNum" @dblclick="handleTap(item)">{{item.totalSize}}</text> 件
						<!-- {{ item.color }} - <text class="txtNum" @dblclick="handleTap(item)">{{item.totalSize}}</text> 件<text class="txtNumStatus" >{{item.checkState}}</text> -->
				  </view>
			  </scroll-view>
		  </view>
		  <!-- <view class="pagin">
			<button class="btn btn-primary" @click="handlePrev" :disabled="current === 1">上一页</button>
			<view class="text">
			  <text class="current">{{ current }}</text>/{{ max }}
			</view>
			<button class="btn btn-primary" @click="handleNext" :disabled="current === max">下一页</button>
		  </view> -->
	</view>
	<view class="mask"></view>
</template>

<script setup>
import { ref, watch,toRaw } from 'vue'
import { onLoad, onShow } from "@dcloudio/uni-app"
	
const emits = defineEmits(['change','receive'])
const props = defineProps({
	pageSize: {
		type: Number,
		default: 20
	},
	total: {
		type: Number,
		default: 0
	},
	page: {
		type: Number,
		default: 1
	},
	title:{
		type:String,
		default:''
	},
	data:{
		type: Object,
		default:null
	},
	listFinished:{
		type: Object,
		default:null
	}
})
let dataList = ref([])
let totalJian = ref(0)
let totalPersent = ref(0)
let TotalList = ref([])
// 监听data
watch(
	//() => props.data,
	[() => props.data, () => props.listFinished],
	([data, listFinished],[dataOld,listFinishedOld]) => {
		dataList.value = toRaw(data);
		let finishedList= toRaw(listFinished);
		 // console.log(finishedList)
		// console.log(finishedList[0].processNum)
		let procNum = "1"
		if(finishedList.length>0){
			procNum =finishedList[0].processNum
		}
		let finishedTotal=0;
		totalJian.value=0;
		totalPersent.value=0;
		// console.log(dataList.value)
		TotalList.value.splice(0)
		
		let preColor="";
		let total = 0
		let finished=0
		// console.log(dataList)
		if(dataList.value.length>0){
			preColor=dataList.value[0].color;
			TotalList.value.push({
				ID:'0',
				color: preColor,
				totalSize:0,
				checkState:"",
				finisedPer:0,
				finished:0,
				style:dataList.value[0].style,
				SizeList:dataList.value[0].SizeList
			})
		}
		// console.log(dataList.value)
		// console.log(TotalList.value)
		// for(let i in dataList.value){
			for (var i=0;i<dataList.value.length;i++)
			{
			/*
			for(let j in dataList.value[i].SizeList){
				// total+=Number(dataList.value[i].SizeList[j].quantity)
				total+=Number(dataList.value[i].SizeList[j].quantity)
			}
			// console.log(finished)
			// console.log(dataList.value[i].color)
			for(let m in finishedList){
				if(finishedList[m].color==dataList.value[i].color){
					finished+=Number(finishedList[m].real_quantity)
				}
			}
			*/
		   total+=Number(dataList.value[i].quantity)
			// console.log(dataList.value[i].color)
			// console.log(total)
			//  console.log(finished)
			// finishedTotal+=finished;
			// console.log(finishedTotal)
			totalJian.value+=total
			dataList.value[i].totalSize = total
			dataList.value[i].finisedPer = parseFloat((finished/(total*Number(procNum))*100).toFixed(1))
			
			let flag = false;
			// console.log(TotalList.value)
			TotalList.value.forEach(function(m,k){
				 // console.log(m)
				 // console.log(total)
				 // console.log(finished)
				 
				if(m.color == dataList.value[i].color){
					// console.log(k)
					// console.log(m.color)
					// console.log(dataList.value[i].color)
					flag=true;
					TotalList.value[k].ID+="|"+dataList.value[i].sizeID;//.ID
					TotalList.value[k].totalSize += total;
					TotalList.value[k].finished =finished;
					TotalList.value[k].finisedPer = parseFloat((TotalList.value[k].finished/(TotalList.value[k].totalSize*Number(procNum))*100).toFixed(1));
					TotalList.value[k].style=dataList.value[0].style;
					TotalList.value[k].SizeList=dataList.value[0].SizeList;
					// console.log(TotalList.value[k].finisedPer)
					// console.log(TotalList.value[k].finished)
					// console.log(TotalList.value[k].totalSize)
					// console.log(Number(procNum))
				}
			})
			if(!flag){
				console.log(dataList.value[i])
				finishedTotal+=finished;
				TotalList.value.push({
					ID:dataList.value[i].sizeID,
					color: dataList.value[i].color,
					totalSize:total,
					checkState:"",
					finished:finished,
					style:dataList.value[i].style,
					SizeList:dataList.value[i].SizeList,
					// style:dataList.value[0].style,
					// SizeList:dataList.value[0].SizeList,
					finisedPer:parseFloat((finished/(total*Number(procNum))*100).toFixed(1))
				})
			}
			
			total = 0
			finished=0
			//console.log(finishedTotal)
		}
		// console.log(TotalList.value)
		if(dataList.value.length>0){
			// console.log(finishedTotal)
			// console.log(totalJian.value)
			// console.log(procNum)
			totalPersent.value =parseFloat((finishedTotal/(totalJian.value*Number(procNum))*100).toFixed(2))
		}
		init()
	}
)

const current = ref(1)
const max = ref(1)
const handleTap = (item) => {
	if(item.checkState==""){
		item.checkState="(已选中)"
	}
	else{
		item.checkState=""
	}
	emits('receive', item)
}
const handlePrev = () => {
	if (current.value === 1) return
	current.value -= 1
	emits('change', {
		type: 'prev',
		value: current.value
	})
}
const handleNext = () => {
	if (current.value === max) return
	current.value += 1
	emits('change', {
		type: 'next',
		value: current.value
	})
}

const init = () => {
	max.value = Math.ceil(props.total / props.pageSize) || 1
	
	// console.log(dataList)
	// console.log(toRaw(props.data))
}

onLoad(({  }) => {
	// console.log(props.data)
	// console.log(toRaw(props.data))
})
onShow(()=>{
	// console.log(props.data)
	// console.log(toRaw(props.data))
})
</script>

<style lang="scss" scoped>
.pagination {
  padding-top: 20rpx;
  position: fixed;
  left: 0;
  right: 0;
  bottom: 120rpx;
  max-height: 24vh;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  background-color: $uni-bg-color;
  box-shadow: 0 6rpx 12rpx 4rpx rgba(0, 0, 0, 0.05),
							0 16rpx 10rpx 2rpx rgba(0, 0, 0, 0.06),
							0 10rpx 10rpx -6rpx rgba(0, 0, 0, 0.05);
  .total {
    display: flex;
    align-items: center;
    justify-content: center;
    color: $uni-text-color-grey;
    height: 20rpx;
	padding-bottom: 30rpx;
	border-bottom: 1px solid gray;
  }
  .totalTitle{
	  // height: 200rpx;
	  white-space: pre-wrap;
	  text-align: center;
	  margin:0 auto;
	  display: flex;
	  flex-direction: row;
	  align-items: center;
	  justify-content:center;
	  margin-bottom: 60rpx;
	  padding-top: 30rpx;
	  // border-bottom: 1px solid gray;
	  .scrollPagination1{
	  	// width: 790rpx;
	  	height: 17vh;
	  	margin: 0;
		width: 200rpx;
		float: left;
		.totalTitleSub1{
			  width: 150rpx;
			  // text-align: left;
			  margin-left: 0rpx;
			  color:red;
			  font-size: 26rpx;
			  padding: 50rpx;
		}
	  }
	  .scrollPagination{
	  	// width: 790rpx;
	  	height: 17vh;
	  	padding: 0;
	  	margin: 0;
		// margin-left: 250rpx;
		right: 10rpx;
		width: auto;
		.totalTitleSub{
			  width: 300rpx;
			  text-align: left;
			  margin-left: 20rpx;
			  margin-top: 10rpx;
		}
	  }
  }
  .pagin {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20rpx;
    height: 80rpx;
    .text {
      font-size: 32rpx;
      .current {
				color: $uni-color-primary;
      }
    }
  }
}
.mask {
  height: 120rpx;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
.txtNum{
	color:#007aff
}
.txtNumStatus{
	color:#ffaa00;
	font-size:30rpx;
}
.txtNumPer{
	// color:#ffaa00
}


</style>